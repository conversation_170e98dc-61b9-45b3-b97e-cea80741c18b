import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

class ConnectivityBanner extends StatefulWidget {
  const ConnectivityBanner({super.key, required this.child});
  final Widget child;

  @override
  State<ConnectivityBanner> createState() => _ConnectivityBannerState();
}

class _ConnectivityBannerState extends State<ConnectivityBanner> {
  bool _offline = false;
  bool _showRestored = false;
  late final Stream<List<ConnectivityResult>> _stream;

  @override
  void initState() {
    super.initState();
    _stream = Connectivity().onConnectivityChanged;
    _stream.listen((events) {
      final off = events.contains(ConnectivityResult.none);
      if (off != _offline && mounted) {
        if (off) {
          // أصبح غير متصل
          setState(() {
            _offline = true;
            _showRestored = false;
          });
        } else {
          // تمت الاستعادة
          setState(() {
            _offline = false;
            _showRestored = true;
          });
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) setState(() => _showRestored = false);
          });
        }
      }
    });

    // فحص أولي
    Connectivity().checkConnectivity().then((resList) =>
        setState(() => _offline = resList.contains(ConnectivityResult.none)));
  }

  @override
  Widget build(BuildContext context) {
    return _ConnectivityInherited(
      offline: _offline,
      child: _buildBanner(),
    );
  }

  Widget _buildBanner() {
    return Stack(
      children: [
        widget.child,
        if (_offline || _showRestored)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              color: _offline ? Colors.red : Colors.green,
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: SafeArea(
                bottom: false,
                child: Center(
                  child: Text(
                    _offline
                        ? 'أنت غير متصل بالإنترنت. تحقق من الاتصال وأعد المحاولة.'
                        : 'تم استعاده الاتصال بالإنترنت',
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _ConnectivityInherited extends InheritedWidget {
  final bool offline;
  const _ConnectivityInherited({required this.offline, required Widget child}) : super(child: child);

  @override
  bool updateShouldNotify(covariant _ConnectivityInherited oldWidget) => offline != oldWidget.offline;
}

extension ConnectivityBannerExt on ConnectivityBanner {
  static bool isOffline(BuildContext context) {
    final inherited = context.dependOnInheritedWidgetOfExactType<_ConnectivityInherited>();
    return inherited?.offline ?? false;
  }
} 