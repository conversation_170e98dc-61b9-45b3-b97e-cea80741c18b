import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/group.dart';
import '../models/post.dart';
import '../widgets/post_card.dart';

class SimpleGroupPage extends StatefulWidget {
  final String groupId;
  const SimpleGroupPage({super.key, required this.groupId});

  @override
  State<SimpleGroupPage> createState() => _SimpleGroupPageState();
}

class _SimpleGroupPageState extends State<SimpleGroupPage> {
  late Future<Group> _futureGroup;
  late Future<List<Post>> _futurePosts;

  @override
  void initState() {
    super.initState();
    _load();
  }

  void _load() {
    _futureGroup = SupabaseService().fetchGroup(widget.groupId);
    _futurePosts = SupabaseService().fetchGroupPosts(widget.groupId);
  }

  Future<void> _showCreatePostSheet() async {
    final controller = TextEditingController();
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('منشور جديد'),
        content: TextField(
          controller: controller,
          maxLines: 4,
          decoration: const InputDecoration(hintText: 'اكتب شيئاً...'),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('نشر')),
        ],
      ),
    );
    if (ok == true && controller.text.trim().isNotEmpty) {
      await SupabaseService().createGroupPost(groupId: widget.groupId, content: controller.text.trim());
      setState(() => _futurePosts = SupabaseService().fetchGroupPosts(widget.groupId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreatePostSheet,
        child: const Icon(Icons.edit),
      ),
      body: FutureBuilder<Group>(
        future: _futureGroup,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
          final g = snapshot.data!;
          return CustomScrollView(
            slivers: [
              SliverAppBar(
                pinned: true,
                expandedHeight: 180,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(g.name),
                  background: g.coverUrl.isNotEmpty
                      ? Image.network(g.coverUrl, fit: BoxFit.cover)
                      : Container(color: Colors.grey.shade300),
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () async {
                      await SupabaseService().toggleGroupMembership(g.id);
                      setState(() => _futureGroup = SupabaseService().fetchGroup(widget.groupId));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: g.joined ? Colors.grey : Theme.of(context).colorScheme.primary,
                    ),
                    child: Text(g.joined ? 'مغادرة' : 'انضمام'),
                  ),
                  const SizedBox(width: 8),
                ],
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(g.description),
                ),
              ),
              FutureBuilder<List<Post>>(
                future: _futurePosts,
                builder: (context, snap) {
                  if (!snap.hasData) {
                    return const SliverFillRemaining(
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }
                  final posts = snap.data!;
                  if (posts.isEmpty) {
                    return const SliverFillRemaining(
                      child: Center(child: Text('لا توجد منشورات بعد')),
                    );
                  }
                  return SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (ctx, i) => PostCard(post: posts[i]),
                      childCount: posts.length,
                    ),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
} 