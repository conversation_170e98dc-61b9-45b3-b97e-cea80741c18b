import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/app_notification.dart';
import 'chat_page.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final _stream = SupabaseService().notificationsStream();

  bool _showUnreadOnly = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات', style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all),
            tooltip: 'تعليم الكل كمقروء',
            onPressed: () async {
              await SupabaseService().markAllNotificationsRead();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ChoiceChip(
                  label: const Text('الكل'),
                  selected: !_showUnreadOnly,
                  onSelected: (v) => setState(() => _showUnreadOnly = false),
                ),
                const SizedBox(width: 8),
                ChoiceChip(
                  label: const Text('غير مقروءة'),
                  selected: _showUnreadOnly,
                  onSelected: (v) => setState(() => _showUnreadOnly = true),
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<List<AppNotification>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                final all = snapshot.data ?? [];
                final notifs = _showUnreadOnly ? all.where((n) => !n.read).toList() : all;
                if (notifs.isEmpty) {
                  return const Center(child: Text('لا توجد إشعارات'));
                }
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: notifs.length,
                  itemBuilder: (context, index) {
                    final n = notifs[index];
                    final icon = _iconFor(n.type);
                    return Dismissible(
                      key: ValueKey(n.id),
                      background: Container(
                        color: n.read ? Colors.orange : Colors.green,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 20),
                        child: Icon(n.read ? Icons.mark_chat_unread : Icons.mark_email_read, color: Colors.white),
                      ),
                      direction: DismissDirection.endToStart,
                      onDismissed: (_) async {
                        await SupabaseService().setNotificationRead(n.id, !n.read);
                      },
                      child: Card(
                        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        color: n.read ? null : Colors.red.shade50,
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundImage: n.senderAvatar.isNotEmpty ? NetworkImage(n.senderAvatar) : null,
                            child: n.senderAvatar.isEmpty ? Text(n.senderName[0]) : null,
                          ),
                          title: Text(_titleFor(n), style: TextStyle(fontWeight: n.read ? FontWeight.normal : FontWeight.bold)),
                          subtitle: Text(_timeAgo(n.createdAt)),
                          trailing: Icon(icon, color: Colors.black),
                          onTap: () async {
                            if (!n.read) await SupabaseService().markNotificationRead(n.id);
                            _handleTap(n);
                          },
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _titleFor(AppNotification n) {
    switch (n.type) {
      case 'like':
        return '${n.senderName} أعجب بمنشورك';
      case 'comment':
        return '${n.senderName} علّق على منشورك';
      case 'follow':
        return '${n.senderName} بدأ متابعتك';
      case 'chat':
        return 'رسالة جديدة من ${n.senderName}';
      case 'app':
        return 'إشعار من التطبيق';
      default:
        return '${n.senderName} بدأ متابعتك';
    }
  }

  String _timeAgo(DateTime date) {
    final diff = DateTime.now().difference(date);
    if (diff.inMinutes < 60) return 'منذ ${diff.inMinutes} دقيقة';
    if (diff.inHours < 24) return 'منذ ${diff.inHours} ساعة';
    return 'منذ ${diff.inDays} يوم';
  }

  IconData _iconFor(String type) {
    switch (type) {
      case 'like':
        return Icons.favorite;
      case 'comment':
        return Icons.comment;
      case 'follow':
        return Icons.person_add;
      case 'chat':
        return Icons.chat;
      case 'app':
        return Icons.notifications;
      default:
        return Icons.notifications;
    }
  }

  void _handleTap(AppNotification n) async {
    if (n.type == 'chat' && n.senderId.isNotEmpty) {
      final chatId = await SupabaseService().getOrCreateChat(n.senderId);
      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ChatPage(
            chatId: chatId,
            otherId: n.senderId,
            username: n.senderName,
            avatarUrl: n.senderAvatar,
          ),
        ),
      );
    }
  }
}

class Notification {
  final NotificationType type;
  final String username;
  final String content;
  final DateTime time;
  bool isRead;

  Notification({
    required this.type,
    required this.username,
    required this.content,
    required this.time,
    required this.isRead,
  });
}

enum NotificationType {
  like,
  comment,
  follow,
} 