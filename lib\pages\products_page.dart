import 'package:flutter/material.dart';
import '../models/product.dart';
import '../supabase_service.dart';
import '../widgets/product_card.dart';
import '../widgets/product_skeleton.dart';
import '../widgets/connectivity_banner.dart';
import 'add_product_page.dart';

class ProductsPage extends StatelessWidget {
  const ProductsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المتجر')),
      body: StreamBuilder<List<Product>>(
        stream: SupabaseService().productsStream(),
        builder: (context, snapshot) {
          final offline = ConnectivityBannerExt.isOffline(context);
          if (!snapshot.hasData || offline) {
            return ListView.builder(
              padding: const EdgeInsets.all(12),
              itemCount: 6,
              itemBuilder: (_, __) => const ProductSkeleton(),
            );
          }
          final products = snapshot.data!;
          if (products.isEmpty) {
            return const Center(child: Text('لا توجد منتجات حتى الآن'));
          }
          return ListView.separated(
            padding: const EdgeInsets.all(12),
            itemCount: products.length,
            separatorBuilder: (_, __) => const SizedBox(height: 8),
            itemBuilder: (ctx, i) => ProductCard(product: products[i]),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(context, MaterialPageRoute(builder: (_) => const AddProductPage()));
        },
        child: const Icon(Icons.add),
      ),
    );
  }
} 