allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}

// Patch legacy uni_links plugin (transitive via app_links) to comply with AGP 8 namespace requirement
subprojects {
    if (name == "uni_links") {
        afterEvaluate {
            val androidExt = extensions.findByName("android")
            if (androidExt != null) {
                try {
                    val clazz = Class.forName("com.android.build.api.dsl.LibraryExtension")
                    if (clazz.isInstance(androidExt)) {
                        val method = clazz.getMethod("setNamespace", String::class.java)
                        method.invoke(androidExt, "name.avioli.unilinks")
                    }
                } catch (ignored: Exception) {
                    // ignore
                }
            }
            // Remove deprecated package attribute from manifest if still present
            val manifestFile = file("src/main/AndroidManifest.xml")
            if (manifestFile.exists()) {
                val text = manifestFile.readText()
                val updated = text.replace("package=\"name.avioli.unilinks\"", "")
                manifestFile.writeText(updated)
            }
        }
    }
}
