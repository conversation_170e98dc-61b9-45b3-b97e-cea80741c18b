class AppNotification {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String type; // follow, like, comment
  final String? refId;
  final DateTime createdAt;
  bool read;

  AppNotification({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.type,
    required this.createdAt,
    this.refId,
    this.read = false,
  });
} 