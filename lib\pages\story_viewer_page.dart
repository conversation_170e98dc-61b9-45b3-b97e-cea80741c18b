import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/story.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import 'chat_page.dart';

class StoryViewerPage extends StatefulWidget {
  final String initialUserId;
  const StoryViewerPage({super.key, required this.initialUserId});

  @override
  State<StoryViewerPage> createState() => _StoryViewerPageState();
}

class _StoryViewerPageState extends State<StoryViewerPage> {
  List<Story> _stories = [];
  int _index = 0;
  bool _liked = false;

  @override
  void initState() {
    super.initState();
    SupabaseService().storiesStream().first.then((list) {
      setState(() {
        _stories = list.where((s) => s.userId == widget.initialUserId).toList();
      });
    });
  }

  void _next() {
    if (_index < _stories.length - 1) {
      setState(() {
        _index++;
        _liked = false;
      });
    } else {
      Navigator.pop(context);
    }
  }

  Future<void> _toggleLike() async {
    final story = _stories[_index];
    await SupabaseService().toggleStoryReaction(storyId: story.id, type: ReactionType.like);
    setState(() => _liked = !_liked);
  }

  Future<void> _replyStory() async {
    final story = _stories[_index];
    final controller = TextEditingController();
    final msg = await showDialog<String>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('رد على القصة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: 'اكتب ردك هنا'),
          autofocus: true,
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, controller.text.trim()), child: const Text('إرسال')),
        ],
      ),
    );
    if (msg != null && msg.isNotEmpty) {
      final chatId = await SupabaseService().getOrCreateChat(story.userId);
      await SupabaseService().sendMessage(chatId: chatId, content: msg);
      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ChatPage(
            chatId: chatId,
            otherId: story.userId,
            username: story.userName,
            avatarUrl: story.userAvatar,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_stories.isEmpty) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    final story = _stories[_index];
    Widget content;
    switch (story.type) {
      case StoryType.text:
        content = Center(
          child: Text(story.text ?? '', style: const TextStyle(fontSize: 28, color: Colors.white)),
        );
        break;
      case StoryType.image:
        content = Image.network(story.mediaUrl ?? '', fit: BoxFit.contain);
        break;
      case StoryType.video:
        content = _VideoPlayer(url: story.mediaUrl ?? '');
        break;
    }

    return GestureDetector(
      onTap: _next,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(child: Stack(children: [
          Center(child: content),
          Positioned(
            top: 16,
            left: 16,
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                if (Supabase.instance.client.auth.currentUser?.id == story.userId)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.redAccent),
                    onPressed: () async {
                      final ok = await showDialog<bool>(
                        context: context,
                        builder: (ctx) => AlertDialog(
                          title: const Text('حذف القصة؟'),
                          content: const Text('لن تتمكن من استعادتها بعد الحذف'),
                          actions: [
                            TextButton(onPressed: ()=>Navigator.pop(ctx,false), child: const Text('إلغاء')),
                            TextButton(onPressed: ()=>Navigator.pop(ctx,true), child: const Text('حذف', style: TextStyle(color: Colors.red)))
                          ],
                        ),
                      );
                      if (ok==true){
                        await SupabaseService().deleteStory(story.id);
                        if (mounted){
                          Navigator.pop(context);
                        }
                      }
                    },
                  ),
              ],
            ),
          ),
          Positioned(
            right: 16,
            bottom: 100,
            child: Column(
              children: [
                IconButton(
                  iconSize: 36,
                  onPressed: _toggleLike,
                  icon: Icon(_liked ? Icons.favorite : Icons.favorite_border, color: Colors.redAccent),
                ),
                const SizedBox(height: 8),
                IconButton(
                  iconSize: 34,
                  onPressed: _replyStory,
                  icon: const Icon(Icons.reply, color: Colors.white),
                ),
              ],
            ),
          ),
        ])),
      ),
    );
  }
}

class _VideoPlayer extends StatefulWidget {
  final String url;
  const _VideoPlayer({required this.url});
  @override
  State<_VideoPlayer> createState() => _VideoPlayerState();
}

class _VideoPlayerState extends State<_VideoPlayer> {
  late VideoPlayerController _controller;
  ChewieController? _chewie;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.url)..initialize().then((_) => setState(() {}));
    _chewie = ChewieController(
      videoPlayerController: _controller,
      autoPlay: true,
      looping: false,
      allowPlaybackSpeedChanging: true,
      playbackSpeeds: const [0.5, 1.0, 1.5, 2.0],
      optionsTranslation: OptionsTranslation(
        playbackSpeedButtonText: 'سرعة التشغيل',
        cancelButtonText: 'إلغاء',
      ),
    );
  }

  @override
  void dispose() {
    _chewie?.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_controller.value.isInitialized) return const CircularProgressIndicator();
    return Chewie(controller: _chewie!);
  }
} 