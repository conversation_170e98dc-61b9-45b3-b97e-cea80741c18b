-- =============================================================
--  Reactions Feature – One-shot Setup Script for Supabase
--  أنشئ التفاعلات الاحترافية ذات 10 ملصقات
-- =============================================================
--  يُنفَّذ هذا الملف مرّة واحدة من تبويب SQL Editor فى لوحة Supabase.
--  سيُنشئ:
--    • نوع ENUM للتفاعلات
--    • جدول reactions موحَّد لكل المحتويات (post/video/story …)
--    • فهرس لأداء أفضل
--    • View لحساب المجاميع لكل نوع
--    • دالة toggle_reaction() لعمل insert/update/delete آلي
--    • صلاحية التنفيذ للمستخدمين المصدَّقين
-- =============================================================

-- ENABLE EXTENSION (قد تكون موجودة مسبقاً)
create extension if not exists "uuid-ossp";

-- OPTIONAL CLEAN-UP (execute safely even if objects don't exist)
-- --------------------------------------------------------------
-- يحذف أى مخطط reactions قديم أو يُعدّله ليتوافق مع الشكل الجديد.
-- إذا كنت تريد الاحتفاظ بالبيانات القديمة، يمكنك إزالة أو تعديل الأوامر أدناه.
-- ستُنفَّذ الأوامر بلا خطأ إن لم تكن العناصر موجودة.

-- 0.1) احذف الـ VIEW و الدالة القديمة (إن وُجدا)
drop view     if exists public.reaction_counts cascade;
drop function if exists public.toggle_reaction(uuid,uuid,text,text) cascade; -- توقيع قديم (إن وُجد)
drop function if exists public.toggle_reaction(uuid,uuid,text,reaction_type) cascade;

-- 0.2) تهيئة جدول reactions القديم ليطابق البنية الجديدة (إن كان موجوداً)
--    أ) أعِد تسمية العمود target → target_id إذا وُجد الهدف ولم يكن target_id موجودًا
--       نجعل العملية داخل كتلة DO للحماية من الأخطاء
do $$
begin
  if exists (
    select 1 from information_schema.columns
    where table_schema = 'public'
      and table_name   = 'reactions'
      and column_name  = 'target'
  )
  and not exists (
    select 1 from information_schema.columns
    where table_schema = 'public'
      and table_name   = 'reactions'
      and column_name  = 'target_id'
  ) then
    alter table public.reactions rename column target to target_id;
  end if;
end $$;

--    ب) تأكّد من وجود القيود والفهارس الصحيحة
alter table if exists public.reactions
  drop constraint if exists reactions_user_id_target_key;

--    ج) إذا كان الجدول لا يزال يفتقد العمود target_id (أو البنية غير متوافقة)
--       احذفه تماماً ليُنشأ من جديد لاحقاً.
do $$
begin
  if exists (
    select 1 from information_schema.columns
    where table_schema = 'public'
      and table_name   = 'reactions'
      and column_name  = 'target'
  ) then
    -- لا يزال العمود القديم موجوداً ولم يُعدَّل
    drop table public.reactions cascade;
  end if;
end $$;

-- 1) ENUM TYPE -------------------------------------------------
--    لاحِظ: لا يمكن تعديل قيم ENUM لاحقاً بسهولة؛ تأكَّد من القائمة قبل التنفيذ.
create type reaction_type as enum (
  'love',
  'laugh',
  'wow',
  'celebrate',
  'insightful',
  'curious',
  'support',
  'sad',
  'angry',
  'idea'
);

-- 2) MAIN TABLE ------------------------------------------------
create table if not exists public.reactions (
  id           uuid primary key default uuid_generate_v4(),
  user_id      uuid references auth.users on delete cascade,
  target_id    uuid not null,               -- ID للمنشور/الفيديو/القصة
  target_kind  text not null check (target_kind in ('post','video','story')),
  type         reaction_type not null,
  created_at   timestamptz     default now(),
  unique (user_id, target_id, target_kind)   -- تفاعل واحد فقط لكل مستخدم/عنصر
);

-- 3) PERFORMANCE INDEX ----------------------------------------
create index if not exists reactions_target_idx on public.reactions (target_id, target_kind);

-- 4) VIEW WITH COUNTS ----------------------------------------
create or replace view public.reaction_counts as
select
  target_id,
  target_kind,
  type,
  count(*)::int as cnt
from public.reactions
group by target_id, target_kind, type;

-- 5) TOGGLE FUNCTION ------------------------------------------
create or replace function public.toggle_reaction(
  p_user        uuid,           -- عادةً auth.uid()
  p_target      uuid,
  p_kind        text,
  p_type        reaction_type   -- "none" لإزالة التفاعل
) returns void language plpgsql security definer set search_path = public as $$
begin
  -- احذف أى تفاعل سابق لنفس المستخدم/العنصر
  delete from public.reactions
  where user_id = p_user and target_id = p_target and target_kind = p_kind;

  -- إذا كان النوع "none" نتوقف عند الحذف فقط
  if p_type <> 'love' and p_type <> 'laugh' and p_type <> 'wow' and
     p_type <> 'celebrate' and p_type <> 'insightful' and p_type <> 'curious' and
     p_type <> 'support' and p_type <> 'sad' and p_type <> 'angry' and p_type <> 'idea' then
    return;  -- حماية إضافية (يجب ألا تصل قيمة غير معرّفة)
  end if;

  -- أضف التفاعل الجديد
  insert into public.reactions(user_id, target_id, target_kind, type)
  values (p_user, p_target, p_kind, p_type);
end;
$$;

-- 6) GRANT EXECUTE TO AUTHENTICATED USERS ---------------------
grant execute on function public.toggle_reaction to authenticated;

-- 7) OPTIONAL: REALTIME BROADCAST -----------------------------
-- إذا أردت بث التغييرات لحظيًا عبر Supabase Realtime:
--   
--   -- فى لوحة Realtime > RLS، أضف قواعد البث على جدول reactions
--   
--   -- أو باستخدام SQL
--   alter publication supabase_realtime add table public.reactions;
--
-- ثم يمكنك الاستماع فى Flutter:
--   Supabase.instance.client
--     .from('reactions')
--     .stream(primaryKey:['id'])
--     .eq('target_id', someId)
--     .listen(...);

-- =============================================================
--  انتهى السكربت.                                               
-- ============================================================= 