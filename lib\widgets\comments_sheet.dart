import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'hierarchical_comment_widget.dart';
import 'dart:io';

class CommentsSheet extends StatefulWidget {
  final Post post;
  final VoidCallback? onCommentAdded;

  const CommentsSheet({
    super.key,
    required this.post,
    this.onCommentAdded,
  });

  @override
  State<CommentsSheet> createState() => _CommentsSheetState();
}

class _CommentsSheetState extends State<CommentsSheet> {
  final TextEditingController _commentController = TextEditingController();
  String? _selectedMediaPath;
  CommentType _selectedMediaType = CommentType.text;
  late Stream<List<Comment>> _stream;
  final ImagePicker _picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();
  bool _sending = false;
  Comment? _replyingTo; // التعليق الذي يتم الرد عليه
  final FocusNode _textFieldFocus = FocusNode();
  List<Comment> _comments = []; // قائمة محلية للتعليقات للتحديث الفوري

  @override
  void initState() {
    super.initState();
    _stream = SupabaseService().commentsStream(widget.post.id);
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _pickMedia(bool isVideo) async {
    final XFile? file = isVideo
        ? await _picker.pickVideo(source: ImageSource.gallery)
        : await _picker.pickImage(source: ImageSource.gallery);

    if (file != null) {
      setState(() {
        _selectedMediaPath = file.path;
        _selectedMediaType = isVideo ? CommentType.video : CommentType.image;
      });
    }
  }



  Future<void> _addComment() async {
    if (_sending) return;
    setState(() => _sending = true);
    String? mediaUrl;

    if (_selectedMediaPath != null) {
      final bytes = await File(_selectedMediaPath!).readAsBytes();
      final ext = _selectedMediaPath!.split('.').last;
      final storagePath = 'comments/${widget.post.id}/${DateTime.now().millisecondsSinceEpoch}.$ext';
      mediaUrl = await SupabaseService().uploadMedia(bytes, storagePath);
    }

    // إنشاء تعليق مؤقت للعرض الفوري
    final currentUser = Supabase.instance.client.auth.currentUser;
    final tempComment = Comment(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      postId: widget.post.id,
      parentId: _replyingTo?.id,
      userId: currentUser?.id ?? '',
      userName: currentUser?.userMetadata?['name'] ?? 'أنت',
      userAvatar: currentUser?.userMetadata?['avatar_url'] ?? '',
      content: _commentController.text.trim().isEmpty && _selectedMediaPath != null
          ? '(ميديا)'
          : _commentController.text.trim(),
      createdAt: DateTime.now(),
      type: _selectedMediaType,
      mediaUrl: mediaUrl,
      depth: _replyingTo != null ? _replyingTo!.depth + 1 : 0,
      replyToUserName: _replyingTo?.userName,
    );

    // إضافة التعليق المؤقت للعرض الفوري
    setState(() {
      if (_replyingTo != null) {
        // إضافة كرد
        _addReplyToComment(_replyingTo!.id, tempComment);
      } else {
        // إضافة كتعليق جديد
        _comments.insert(0, tempComment);
      }
    });

    try {
      if (_replyingTo != null) {
        // إنشاء رد على تعليق
        await SupabaseService().createReply(
          postId: widget.post.id,
          parentCommentId: _replyingTo!.id,
          content: _commentController.text.trim().isEmpty && _selectedMediaPath != null
              ? '(ميديا)'
              : _commentController.text.trim(),
          type: _selectedMediaType,
          mediaUrl: mediaUrl,
        );
      } else {
        // إنشاء تعليق جديد
        await SupabaseService().createComment(
          postId: widget.post.id,
          content: _commentController.text.trim().isEmpty && _selectedMediaPath != null
              ? '(ميديا)'
              : _commentController.text.trim(),
          type: _selectedMediaType,
          mediaUrl: mediaUrl,
        );
      }
    } catch (e) {
      // إزالة التعليق المؤقت في حالة الخطأ
      setState(() {
        if (_replyingTo != null) {
          _removeReplyFromComment(_replyingTo!.id, tempComment.id);
        } else {
          _comments.removeWhere((c) => c.id == tempComment.id);
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل نشر التعليق: $e')),
        );
      }
    }

    _commentController.clear();
    setState(() {
      _selectedMediaPath = null;
      _selectedMediaType = CommentType.text;
      _replyingTo = null; // إلغاء الرد
    });

    if (mounted) setState(() => _sending = false);

    // تحديث عدد التعليقات في الواجهة الرئيسية
    if (widget.onCommentAdded != null) {
      widget.onCommentAdded!();
    }
  }

  void _addReplyToComment(String parentId, Comment reply) {
    for (int i = 0; i < _comments.length; i++) {
      final comment = _comments[i];
      if (comment.id == parentId) {
        _comments[i] = comment.copyWith(
          replies: [...comment.replies, reply],
        );
        return;
      }
      // البحث في الردود المتداخلة
      _addReplyToCommentRecursive(comment, parentId, reply);
    }
  }

  void _addReplyToCommentRecursive(Comment comment, String parentId, Comment reply) {
    for (int i = 0; i < comment.replies.length; i++) {
      final childComment = comment.replies[i];
      if (childComment.id == parentId) {
        comment.replies[i] = childComment.copyWith(
          replies: [...childComment.replies, reply],
        );
        return;
      }
      _addReplyToCommentRecursive(childComment, parentId, reply);
    }
  }

  void _removeReplyFromComment(String parentId, String replyId) {
    for (int i = 0; i < _comments.length; i++) {
      final comment = _comments[i];
      if (comment.id == parentId) {
        _comments[i] = comment.copyWith(
          replies: comment.replies.where((r) => r.id != replyId).toList(),
        );
        return;
      }
      _removeReplyFromCommentRecursive(comment, parentId, replyId);
    }
  }

  void _removeReplyFromCommentRecursive(Comment comment, String parentId, String replyId) {
    for (int i = 0; i < comment.replies.length; i++) {
      final childComment = comment.replies[i];
      if (childComment.id == parentId) {
        comment.replies[i] = childComment.copyWith(
          replies: childComment.replies.where((r) => r.id != replyId).toList(),
        );
        return;
      }
      _removeReplyFromCommentRecursive(childComment, parentId, replyId);
    }
  }

  void _replyToComment(Comment comment) {
    setState(() {
      _replyingTo = comment;
    });
    _textFieldFocus.requestFocus();
  }

  void _cancelReply() {
    setState(() {
      _replyingTo = null;
    });
  }

  Future<void> _toggleCommentReaction(Comment comment, ReactionType reaction) async {
    try {
      await SupabaseService().toggleCommentReaction(
        commentId: comment.id,
        reaction: reaction,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في التفاعل: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: StreamBuilder<List<Comment>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting && _comments.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError && _comments.isEmpty) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }

                // استخدام البيانات من Stream إذا كانت متوفرة، وإلا استخدام القائمة المحلية
                List<Comment> comments = _comments;
                if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  // دمج التعليقات المؤقتة مع البيانات الحقيقية
                  final realComments = snapshot.data!;
                  final tempComments = _comments.where((c) => c.id.startsWith('temp_')).toList();

                  // إزالة التعليقات المؤقتة التي تم حفظها فعلياً
                  final filteredTempComments = tempComments.where((temp) {
                    return !realComments.any((real) =>
                      real.content == temp.content &&
                      real.userId == temp.userId &&
                      real.parentId == temp.parentId
                    );
                  }).toList();

                  comments = [...filteredTempComments, ...realComments];
                  _comments = comments; // تحديث القائمة المحلية
                }

                if (comments.isEmpty) {
                  return const Center(child: Text('لا توجد تعليقات'));
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: comments.length,
                  itemBuilder: (context, index) {
                    final comment = comments[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: HierarchicalCommentWidget(
                        comment: comment,
                        onReply: _replyToComment,
                        onReaction: _toggleCommentReaction,
                        maxDepth: 10,
                        showConnectorLines: true,
                      ),
                    );
                  },
                );
              },
            ),
          ),
          if (_selectedMediaPath != null)
            Container(
              height: 100,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (_selectedMediaType == CommentType.image)
                    Image.file(
                      File(_selectedMediaPath!),
                      fit: BoxFit.cover,
                    )
                  else
                    const Icon(Icons.video_file, size: 50),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        setState(() {
                          _selectedMediaPath = null;
                          _selectedMediaType = CommentType.text;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          SafeArea(
            child: Column(
              children: [
                // واجهة الرد على التعليق
                if (_replyingTo != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      border: Border(
                        bottom: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.reply, color: Colors.blue[600], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'رد على ${_replyingTo!.userName}',
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.close, color: Colors.grey[600], size: 18),
                          onPressed: _cancelReply,
                          constraints: const BoxConstraints(),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ),
                  ),

                // حقل النص والأزرار
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.red,
                        child: Text('م'),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _commentController,
                          focusNode: _textFieldFocus,
                          decoration: InputDecoration(
                            hintText: _replyingTo != null
                                ? 'اكتب رداً...'
                                : 'اكتب تعليقاً...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Colors.grey[100],
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          maxLines: null,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.image, color: Colors.red),
                        onPressed: () => _pickMedia(false),
                      ),
                      IconButton(
                        icon: const Icon(Icons.videocam, color: Colors.red),
                        onPressed: () => _pickMedia(true),
                      ),
                      IconButton(
                        icon: const Icon(Icons.send, color: Colors.red),
                        onPressed: _sending ? null : _addComment,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}