import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/group.dart';
import 'simple_group_page.dart';

class SimpleGroupsPage extends StatefulWidget {
  const SimpleGroupsPage({super.key});

  @override
  State<SimpleGroupsPage> createState() => _SimpleGroupsPageState();
}

class _SimpleGroupsPageState extends State<SimpleGroupsPage> {
  late Future<List<Group>> _futureGroups;

  @override
  void initState() {
    super.initState();
    _load();
  }

  void _load() {
    setState(() => _futureGroups = SupabaseService().fetchGroups());
  }

  Future<void> _createGroup() async {
    final nameController = TextEditingController();
    final descController = TextEditingController();

    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('إنشاء مجموعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'اسم المجموعة'),
            ),
            TextField(
              controller: descController,
              decoration: const InputDecoration(labelText: 'وصف'),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('إنشاء')),
        ],
      ),
    );

    if (ok == true && nameController.text.trim().isNotEmpty) {
      await SupabaseService().createGroup(
        name: nameController.text.trim(),
        description: descController.text.trim(),
      );
      _load();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المجموعات')),
      floatingActionButton: FloatingActionButton(
        onPressed: _createGroup,
        child: const Icon(Icons.group_add),
      ),
      body: FutureBuilder<List<Group>>(
        future: _futureGroups,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
          final groups = snapshot.data!;
          if (groups.isEmpty) return const Center(child: Text('لا توجد مجموعات بعد'));
          return RefreshIndicator(
            onRefresh: () async => _load(),
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemCount: groups.length,
              separatorBuilder: (_, __) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final g = groups[index];
                return Card(
                  child: ListTile(
                    onTap: () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => SimpleGroupPage(groupId: g.id)),
                      );
                      _load();
                    },
                    title: Text(g.name),
                    subtitle: Text('${g.membersCount} أعضاء'),
                    trailing: ElevatedButton(
                      onPressed: () async {
                        await SupabaseService().toggleGroupMembership(g.id);
                        _load();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: g.joined ? Colors.grey : Theme.of(context).colorScheme.primary,
                      ),
                      child: Text(g.joined ? 'مغادرة' : 'انضمام'),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
} 