import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class LiveStreamSetupPage extends StatefulWidget {
  const LiveStreamSetupPage({super.key});

  @override
  State<LiveStreamSetupPage> createState() => _LiveStreamSetupPageState();
}

class _LiveStreamSetupPageState extends State<LiveStreamSetupPage> {
  bool _connecting = false;

  Future<void> _startBroadcast() async {
    final cam = await Permission.camera.request();
    final mic = await Permission.microphone.request();
    if (!cam.isGranted || !mic.isGranted) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('يجب منح صلاحيات الكاميرا والميكروفون')));
      return;
    }

    setState(() => _connecting = true);
    try {
      final supa = Supabase.instance.client;
      final uid = supa.auth.currentUser?.id;
      if (uid == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // 1️⃣ الحصول على بيانات الاتصال من الدالة
      final resp = await supa.functions.invoke('livekit_token', body: {'identity': uid});
      if (resp.data == null || resp.status != 200) {
        throw 'خطأ إنشاء التوكن: ${resp.data}';
      }

      final String url = resp.data['url'] as String;
      final String token = resp.data['token'] as String;

      // 2️⃣ الاتصال بغرفة LiveKit
      final room = Room();
      await room.connect(url, token);
      await room.localParticipant?.setCameraEnabled(true);
      await room.localParticipant?.setMicrophoneEnabled(true);

      if (!mounted) return;
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (_) => LiveStreamBroadcastPage(room: room)),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل بدء البث: $e')));
    } finally {
      if (mounted) setState(() => _connecting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('بدء بث مباشر')),
      body: Center(
        child: _connecting
            ? const CircularProgressIndicator()
            : ElevatedButton.icon(
                onPressed: _startBroadcast,
                icon: const Icon(Icons.wifi_tethering),
                label: const Text('ابدأ البث'),
              ),
      ),
    );
  }
}

class LiveStreamBroadcastPage extends StatefulWidget {
  final Room room;
  const LiveStreamBroadcastPage({super.key, required this.room});

  @override
  State<LiveStreamBroadcastPage> createState() => _LiveStreamBroadcastPageState();
}

class _LiveStreamBroadcastPageState extends State<LiveStreamBroadcastPage> {
  VideoTrack? _track;
  int _viewerCount = 1;

  bool _micMuted = false;
  bool _camOff = false;

  LocalVideoTrack? get _localCamTrack {
    final pubs = widget.room.localParticipant?.videoTrackPublications ?? [];
    if (pubs.isEmpty) return null;
    final t = pubs.first.track;
    return t is LocalVideoTrack ? t : null;
  }

  @override
  void initState() {
    super.initState();
    // استمع لتغيّرات الغرفة لاستخراج مسار الفيديو وعدد المشاهدين
    widget.room.addListener(_onRoomChanged);
    _updateState();
  }

  void _onRoomChanged() => _updateState();

  void _updateState() {
    _updateTrack();
    setState(() {
      _viewerCount = widget.room.remoteParticipants.length + 1; // +1 للبثّ المحلي
    });
  }

  void _updateTrack() {
    final pubs = widget.room.localParticipant?.videoTrackPublications ?? [];
    for (final pub in pubs) {
      final trk = pub.track;
      if (trk is VideoTrack && !pub.muted) {
        _track = trk;
        return;
      }
    }
  }

  Future<void> _toggleMic() async {
    _micMuted = !_micMuted;
    await widget.room.localParticipant?.setMicrophoneEnabled(!_micMuted);
    if (mounted) setState(() {});
  }

  Future<void> _toggleCam() async {
    _camOff = !_camOff;
    await widget.room.localParticipant?.setCameraEnabled(!_camOff);
    if (mounted) setState(() {});
  }

  Future<void> _switchCamera() async {
    final camTrack = _localCamTrack;
    if (camTrack != null) {
      await camTrack.switchCamera('');
    }
  }

  Future<void> _endStream() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('إنهاء البث؟'),
        content: const Text('هل تريد إنهاء البث المباشر؟'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('متابعة')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('إنهاء')),        ],
      ),
    );
    if (confirm != true) return;

    await widget.room.disconnect();

    if (!mounted) return;

    final publish = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('نشر التسجيل؟'),
        content: const Text('تم إنهاء البث. هل ترغب في نشر التسجيل كمنشور؟'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('لاحقًا')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('نشر')),
        ],
      ),
    );

    if (publish == true) {
      // TODO: تحميل التسجيل إلى التخزين وإنشاء منشور
    }

    if (mounted) Navigator.pop(context);
  }

  @override
  void dispose() {
    widget.room.removeListener(_onRoomChanged);
    widget.room.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بث مباشر'),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () async {
              await widget.room.disconnect();
              if (mounted) Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // عرض فيديو الكاميرا الحي
          if (_track != null)
            Positioned.fill(child: VideoTrackRenderer(_track!))
          else
            const Center(child: CircularProgressIndicator()),

          // عدّاد المشاهدين فى أعلى اليسار
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(color: Colors.black45, borderRadius: BorderRadius.circular(8)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.remove_red_eye, color: Colors.white, size: 16),
                  const SizedBox(width: 4),
                  Text('$_viewerCount', style: const TextStyle(color: Colors.white)),
                ],
              ),
            ),
          ),

          // طبقة التعليقات المباشرة
          Align(
            alignment: Alignment.bottomCenter,
            child: _LiveChatOverlay(channelId: widget.room.name ?? 'live_chat'),
          ),

          // أدوات التحكم السفلية
          Positioned(
            bottom: 260,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black45,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(_micMuted ? Icons.mic_off : Icons.mic, color: Colors.white),
                      onPressed: _toggleMic,
                    ),
                    IconButton(
                      icon: Icon(_camOff ? Icons.videocam_off : Icons.videocam, color: Colors.white),
                      onPressed: _toggleCam,
                    ),
                    IconButton(
                      icon: const Icon(Icons.cameraswitch, color: Colors.white),
                      onPressed: _switchCamera,
                    ),
                    IconButton(
                      icon: const Icon(Icons.stop_circle, color: Colors.red),
                      onPressed: _endStream,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _LiveChatOverlay extends StatefulWidget {
  final String channelId;
  const _LiveChatOverlay({super.key, required this.channelId});

  @override
  State<_LiveChatOverlay> createState() => _LiveChatOverlayState();
}

class _LiveChatOverlayState extends State<_LiveChatOverlay> {
  final _controller = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  RealtimeChannel? _channel;

  @override
  void initState() {
    super.initState();
    _setupChannel();
  }

  Future<void> _setupChannel() async {
    final supa = Supabase.instance.client;
    final channel = supa.channel(
      'live_chat:${widget.channelId}',
      opts: const RealtimeChannelConfig(self: true),
    );

    channel.onBroadcast(event: 'message', callback: (payload) {
      if (payload is Map<String, dynamic>) {
        setState(() => _messages.add(payload));
      }
    });

    channel.subscribe();
    setState(() => _channel = channel);
  }

  Future<void> _sendMessage() async {
    final text = _controller.text.trim();
    if (text.isEmpty || _channel == null) return;
    _controller.clear();
    final supa = Supabase.instance.client;
    final payload = {
      'uid': supa.auth.currentUser?.id,
      'text': text,
      'ts': DateTime.now().toIso8601String(),
    };
    await _channel!.sendBroadcastMessage(event: 'message', payload: payload);
  }

  @override
  void dispose() {
    _channel?.unsubscribe();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250,
      color: Colors.black.withOpacity(0.4),
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              reverse: true,
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final msg = _messages[_messages.length - 1 - index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    msg['text'] as String? ?? '',
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              },
            ),
          ),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'اكتب تعليقًا...',
                    hintStyle: TextStyle(color: Colors.white70),
                    border: InputBorder.none,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.send, color: Colors.white),
                onPressed: _sendMessage,
              ),
            ],
          ),
        ],
      ),
    );
  }
} 