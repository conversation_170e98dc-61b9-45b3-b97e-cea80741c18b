import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:math';
import 'package:just_waveform/just_waveform.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class FeedVideoPlayer extends StatefulWidget {
  final String url;
  const FeedVideoPlayer({super.key, required this.url});

  @override
  State<FeedVideoPlayer> createState() => _FeedVideoPlayerState();
}

class _FeedVideoPlayerState extends State<FeedVideoPlayer> {
  late VideoPlayerController _videoController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _videoController = VideoPlayerController.network(widget.url);
    _videoController.initialize().then((_) {
      _chewieController = ChewieController(
        videoPlayerController: _videoController,
        autoPlay: false,
        looping: false,
        allowMuting: true,
        allowPlaybackSpeedChanging: true,
        playbackSpeeds: const [0.5, 1.0, 1.5, 2.0],
        optionsTranslation: OptionsTranslation(
          playbackSpeedButtonText: 'سرعة التشغيل',
          cancelButtonText: 'إلغاء',
        ),
        additionalOptions: (context) => [],
      );
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_chewieController == null || !_videoController.value.isInitialized) {
      return Container(
        height: 250,
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    // حساب نسبة العرض إلى الارتفاع للفيديو
    final videoSize = _videoController.value.size;
    final isVerticalVideo = videoSize.height > videoSize.width;

    // إذا كان الفيديو عمودي، اجعله يمتد بعرض الشاشة كاملاً مثل فيسبوك
    if (isVerticalVideo) {
      final screenWidth = MediaQuery.of(context).size.width;
      final videoAspectRatio = videoSize.width / videoSize.height;
      final calculatedHeight = screenWidth / videoAspectRatio;

      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: GestureDetector(
          onTap: () => _openFull(context),
          child: SizedBox(
            height: calculatedHeight.clamp(200.0, 500.0), // حد أدنى وأقصى للارتفاع
            width: double.infinity,
            child: Chewie(controller: _chewieController!),
          ),
        ),
      );
    }

    // للفيديوهات الأفقية، احتفظ بالتصميم الحالي
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: GestureDetector(
        onTap: () => _openFull(context),
        child: SizedBox(
          height: 250,
          width: double.infinity,
          child: Chewie(controller: _chewieController!),
        ),
      ),
    );
  }

  void _openFull(BuildContext context) {
    if (_chewieController == null) return;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: Center(
              child: Chewie(controller: _chewieController!),
            ),
          ),
        ),
      ),
    );
  }
}

class FeedImage extends StatelessWidget {
  final String url;
  const FeedImage({super.key, required this.url});

  void _openFull(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: InteractiveViewer(
              child: Image.network(url),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _openFull(context),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              url,
              fit: BoxFit.cover,
              height: 250,
              width: double.infinity,
              loadingBuilder: (context, child, progress) {
                if (progress == null) return child;
                return SizedBox(
                  height: 250,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: progress.expectedTotalBytes != null
                          ? progress.cumulativeBytesLoaded / progress.expectedTotalBytes!
                          : null,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stack) => const SizedBox(
                height: 250,
                child: Center(child: Icon(Icons.broken_image, size: 80)),
              ),
            ),
          ),
        ),
        Positioned(
          top: 8,
          left: 8,
          child: InkWell(
            onTap: () => _openFull(context),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black45,
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.fullscreen, color: Colors.white, size: 20),
            ),
          ),
        ),
      ],
    );
  }
}

class LinkPreview extends StatelessWidget {
  final String url;
  final Map<String, dynamic>? meta;
  const LinkPreview({super.key, required this.url, this.meta});

  @override
  Widget build(BuildContext context) {
    final title = meta?['title'] ?? url;
    final image = meta?['image'];
    final description = meta?['description'];
    return GestureDetector(
      onTap: () async {
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            if (image != null)
              ClipRRect(
                borderRadius: const BorderRadius.horizontal(left: Radius.circular(10)),
                child: Image.network(image, width: 100, height: 100, fit: BoxFit.cover),
              ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, maxLines: 2, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.bold)),
                    if (description != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(description, maxLines: 2, overflow: TextOverflow.ellipsis, style: TextStyle(color: Colors.grey[600])),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(url, maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(color: Colors.blue[700], fontSize: 12)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FeedAudioPlayer extends StatefulWidget {
  final String url;
  final bool isVoice;
  const FeedAudioPlayer({super.key, required this.url, this.isVoice = false});

  @override
  State<FeedAudioPlayer> createState() => _FeedAudioPlayerState();
}

class _FeedAudioPlayerState extends State<FeedAudioPlayer> with SingleTickerProviderStateMixin {
  late final AudioPlayer _player;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  double _speed = 1.0;

  final int _bars = 20;
  late final AnimationController _vizController;
  Waveform? _waveform;
  List<double>? _barSamples; // 0..1 amplitude per bar
  bool _waveLoading = false;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _player.setSourceUrl(widget.url);
    _player.onDurationChanged.listen((d) => setState(() => _duration = d));
    _player.onPositionChanged.listen((p) => setState(() => _position = p));
    _player.onPlayerComplete.listen((_) {
      _vizController.stop();
      _vizController.reset();
      setState(() {
        _isPlaying = false;
        _position = Duration.zero;
      });
    });
    _vizController = AnimationController(vsync: this, duration: const Duration(milliseconds: 800))
      ..addListener(() => setState(() {}));

    _prepareWaveform();
  }

  @override
  void dispose() {
    _player.dispose();
    _vizController.dispose();
    super.dispose();
  }

  void _toggle() async {
    if (_isPlaying) {
      await _player.pause();
    } else {
      await _player.resume();
    }
    setState(() => _isPlaying = !_isPlaying);

    if (_isPlaying) {
      _vizController.repeat();
    } else {
      _vizController.stop();
      _vizController.reset();
    }
  }

  void _changeSpeed() async {
    if (_speed == 1.0) {
      _speed = 1.5;
    } else if (_speed == 1.5) {
      _speed = 2.0;
    } else {
      _speed = 1.0;
    }
    await _player.setPlaybackRate(_speed);
    setState(() {});
  }

  Future<void> _prepareWaveform() async {
    if (_waveLoading) return;
    _waveLoading = true;
    try {
      final dir = await getTemporaryDirectory();
      final audioFile = File('${dir.path}/aud_${DateTime.now().millisecondsSinceEpoch}.tmp');

      // download
      final response = await http.get(Uri.parse(widget.url));
      if (response.statusCode == 200) {
        await audioFile.writeAsBytes(response.bodyBytes);
        final waveFile = File('${audioFile.path}.wave');
        final stream = JustWaveform.extract(
          audioInFile: audioFile,
          waveOutFile: waveFile,
        );
        await for (final progress in stream) {
          if (progress.waveform != null) {
            _waveform = progress.waveform;
            _computeBarSamples();
            break;
          }
        }
      }
    } catch (_) {
      // ignore errors, fallback to sine animation
    }
    if (mounted) setState(() {});
  }

  void _computeBarSamples() {
    if (_waveform == null) return;

    final totalMs = _waveform!.duration.inMilliseconds;
    final is16Bit = _waveform!.flags == 0; // 0 => 16-bit samples, else 8-bit
    final double denom = is16Bit ? 65535.0 : 255.0;

    _barSamples = List.generate(_bars, (i) {
      final double startFrac = i / _bars;
      final double endFrac = (i + 1) / _bars;

      final startPixel = _waveform!.positionToPixel(Duration(milliseconds: (totalMs * startFrac).floor())).toInt();
      final endPixel = _waveform!.positionToPixel(Duration(milliseconds: (totalMs * endFrac).ceil())).toInt();

      int maxAmp = 0;
      for (int p = startPixel; p <= endPixel; p++) {
        final amp = _waveform!.getPixelMax(p).abs();
        if (amp > maxAmp) maxAmp = amp;
      }

      return maxAmp / denom; // normalised 0..1
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isVoice
              ? [Colors.indigo.shade50, Colors.indigo.shade100]
              : [Colors.grey.shade200, Colors.grey.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 4, offset: const Offset(0, 2)),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(_isPlaying ? Icons.pause_circle : Icons.play_circle, size: 36, color: widget.isVoice ? Colors.indigo : Colors.blueGrey),
            onPressed: _toggle,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: widget.isVoice ? Colors.indigo : Colors.blueGrey,
                    inactiveTrackColor: (widget.isVoice ? Colors.indigo : Colors.blueGrey).withOpacity(0.3),
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                  ),
                  child: Slider(
                    value: _position.inMilliseconds.toDouble().clamp(0, _duration.inMilliseconds.toDouble()),
                    max: _duration.inMilliseconds.toDouble().clamp(1, double.infinity),
                    onChanged: (v) async {
                      final pos = Duration(milliseconds: v.toInt());
                      await _player.seek(pos);
                    },
                  ),
                ),
                SizedBox(
                  height: 20,
                  child: Row(
                    children: List.generate(_bars, (i) {
                      final progressFraction = _duration.inMilliseconds == 0 ? 0.0 : _position.inMilliseconds / _duration.inMilliseconds;
                      final activeIndex = (progressFraction * _bars).floor();

                      double amp = 0.2;
                      if (_barSamples != null && i < _barSamples!.length) {
                        amp = max(0.05, _barSamples![i]);
                      } else if (_isPlaying) {
                        // fallback sine if samples not ready
                        final phase = _vizController.value * 2 * pi;
                        amp = (sin(phase + i) + 1) / 2 * 0.8 + 0.2;
                      }

                      final double h = 4 + amp * 16;
                      final Color base = widget.isVoice ? Colors.indigo : Colors.blueGrey;
                      final Color barColor = i <= activeIndex ? base : base.withOpacity(0.4);

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 1.5),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 120),
                          width: 3,
                          height: h,
                          decoration: BoxDecoration(
                            color: barColor,
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                Text('${_format(_position)} / ${_format(_duration)}', style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
          TextButton(
            onPressed: _changeSpeed,
            child: Text('${_speed.toStringAsFixed(_speed == 2.0 ? 0 : 1)}x', style: TextStyle(color: widget.isVoice ? Colors.indigo : Colors.blueGrey)),
          ),
          if (widget.isVoice)
            const Padding(
              padding: EdgeInsets.only(left: 8.0),
              child: Icon(Icons.mic, size: 20, color: Colors.red),
            ),
        ],
      ),
    );
  }

  String _format(Duration d) {
    final m = d.inMinutes.remainder(60).toString().padLeft(2, '0');
    final s = d.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$m:$s';
  }
} 