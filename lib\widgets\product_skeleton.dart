import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ProductSkeleton extends StatelessWidget {
  const ProductSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final base = Colors.grey.shade300;
    final highlight = Colors.grey.shade100;
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      child: Shimmer.fromColors(
        baseColor: base,
        highlightColor: highlight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة رئيسية
            Container(height: 180, color: Colors.white),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(height: 12, width: 150, color: Colors.white),
                  const SizedBox(height: 6),
                  Container(height: 12, width: 100, color: Colors.white),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
} 