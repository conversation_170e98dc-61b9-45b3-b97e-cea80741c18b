-- =============================================================
--  Comments Counter Setup Script for Supabase
--  إعداد عدادات التعليقات التلقائية
-- =============================================================
--  يُنفَّذ هذا الملف مرّة واحدة من تبويب SQL Editor فى لوحة Supabase.
--  سيُنشئ:
--    • دالة increment_post_comments() لتحديث عدد التعليقات
--    • دالة update_comments_count() لإعادة حساب العدد
--    • تريجر لتحديث العدد تلقائياً عند إضافة/حذف تعليق
-- =============================================================

-- 1) دالة لتحديث عدد التعليقات في المنشور
CREATE OR REPLACE FUNCTION public.increment_post_comments(post_id_param UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- تحديث عدد التعليقات بناءً على العدد الفعلي في جدول التعليقات
  UPDATE posts 
  SET comments_count = (
    SELECT COUNT(*) 
    FROM comments 
    WHERE post_id = post_id_param
  )
  WHERE id = post_id_param;
END;
$$;

-- 2) دالة لإعادة حساب عدد التعليقات لجميع المنشورات
CREATE OR REPLACE FUNCTION public.update_all_posts_comments_count()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- تحديث عدد التعليقات لجميع المنشورات
  UPDATE posts 
  SET comments_count = (
    SELECT COUNT(*) 
    FROM comments 
    WHERE comments.post_id = posts.id
  );
END;
$$;

-- 3) دالة تريجر لتحديث عدد التعليقات تلقائياً
CREATE OR REPLACE FUNCTION public.update_post_comments_count_trigger()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- في حالة الإدراج أو التحديث
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    UPDATE posts 
    SET comments_count = (
      SELECT COUNT(*) 
      FROM comments 
      WHERE post_id = NEW.post_id
    )
    WHERE id = NEW.post_id;
    RETURN NEW;
  END IF;
  
  -- في حالة الحذف
  IF TG_OP = 'DELETE' THEN
    UPDATE posts 
    SET comments_count = (
      SELECT COUNT(*) 
      FROM comments 
      WHERE post_id = OLD.post_id
    )
    WHERE id = OLD.post_id;
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$;

-- 4) إنشاء التريجر على جدول التعليقات
DROP TRIGGER IF EXISTS comments_count_trigger ON public.comments;
CREATE TRIGGER comments_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.comments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_post_comments_count_trigger();

-- 5) دالة مماثلة للمنتجات
CREATE OR REPLACE FUNCTION public.increment_product_comments(product_id_param UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- تحديث عدد التعليقات بناءً على العدد الفعلي في جدول تعليقات المنتجات
  UPDATE products 
  SET comments_count = (
    SELECT COUNT(*) 
    FROM product_comments 
    WHERE product_id = product_id_param
  )
  WHERE id = product_id_param;
END;
$$;

-- 6) تريجر للمنتجات
CREATE OR REPLACE FUNCTION public.update_product_comments_count_trigger()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- في حالة الإدراج أو التحديث
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    UPDATE products 
    SET comments_count = (
      SELECT COUNT(*) 
      FROM product_comments 
      WHERE product_id = NEW.product_id
    )
    WHERE id = NEW.product_id;
    RETURN NEW;
  END IF;
  
  -- في حالة الحذف
  IF TG_OP = 'DELETE' THEN
    UPDATE products 
    SET comments_count = (
      SELECT COUNT(*) 
      FROM product_comments 
      WHERE product_id = OLD.product_id
    )
    WHERE id = OLD.product_id;
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$;

-- 7) إنشاء التريجر على جدول تعليقات المنتجات
DROP TRIGGER IF EXISTS product_comments_count_trigger ON public.product_comments;
CREATE TRIGGER product_comments_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.product_comments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_product_comments_count_trigger();

-- 8) منح صلاحيات التنفيذ للمستخدمين المصدقين
GRANT EXECUTE ON FUNCTION public.increment_post_comments TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_all_posts_comments_count TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_product_comments TO authenticated;

-- 9) تحديث العدادات الحالية (اختياري - يمكن تشغيله لإصلاح البيانات الموجودة)
-- SELECT public.update_all_posts_comments_count();

-- =============================================================
--  انتهى السكربت.
--  ملاحظة: بعد تشغيل هذا السكربت، ستتم إدارة عدادات التعليقات تلقائياً
--  عبر التريجرز، ولن تحتاج لتحديثها يدوياً في التطبيق.
-- =============================================================
