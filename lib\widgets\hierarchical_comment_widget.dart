import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';

class HierarchicalCommentWidget extends StatefulWidget {
  final Comment comment;
  final Function(Comment) onReply;
  final Function(Comment)? onLike;
  final int maxDepth;
  final bool showConnectorLines;

  const HierarchicalCommentWidget({
    super.key,
    required this.comment,
    required this.onReply,
    this.onLike,
    this.maxDepth = 10,
    this.showConnectorLines = true,
  });

  @override
  State<HierarchicalCommentWidget> createState() => _HierarchicalCommentWidgetState();
}

class _HierarchicalCommentWidgetState extends State<HierarchicalCommentWidget> {
  bool _showReplies = true;
  bool _isLiking = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // التعليق الأساسي
        _buildCommentItem(widget.comment),
        
        // الردود مع التسلسل الهرمي
        if (_showReplies && widget.comment.replies.isNotEmpty)
          _buildRepliesSection(),
      ],
    );
  }

  Widget _buildCommentItem(Comment comment) {
    final isReply = comment.depth > 0;
    
    return Container(
      margin: EdgeInsets.only(
        right: comment.depth * 24.0, // إزاحة حسب العمق
        bottom: 8,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // خطوط الاتصال (مثل فيسبوك)
          if (isReply && widget.showConnectorLines)
            _buildConnectorLines(comment),
          
          // صورة المستخدم
          CircleAvatar(
            radius: isReply ? 14 : 18,
            backgroundImage: comment.userAvatar.isNotEmpty
                ? NetworkImage(comment.userAvatar)
                : null,
            backgroundColor: Colors.grey[300],
            child: comment.userAvatar.isEmpty
                ? Icon(
                    Icons.person,
                    size: isReply ? 16 : 20,
                    color: Colors.grey[600],
                  )
                : null,
          ),
          
          const SizedBox(width: 8),
          
          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // فقاعة التعليق
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المستخدم
                      Text(
                        comment.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: isReply ? 13 : 14,
                          color: Colors.blue[800],
                        ),
                      ),
                      
                      // إشارة للمستخدم المُرد عليه
                      if (comment.replyToUserName != null)
                        Text(
                          'رداً على ${comment.replyToUserName}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      
                      const SizedBox(height: 4),
                      
                      // محتوى التعليق
                      Text(
                        comment.content,
                        style: TextStyle(
                          fontSize: isReply ? 13 : 14,
                        ),
                      ),
                      
                      // الميديا إن وجدت
                      if (comment.mediaUrl != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: _buildMediaContent(comment),
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // أزرار الإجراءات
                _buildActionButtons(comment),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConnectorLines(Comment comment) {
    return Container(
      width: 24,
      height: 60,
      margin: const EdgeInsets.only(left: 8),
      child: CustomPaint(
        painter: ConnectorLinesPainter(
          depth: comment.depth,
          isLast: false, // يمكن تحسين هذا لاحقاً
        ),
      ),
    );
  }

  Widget _buildMediaContent(Comment comment) {
    if (comment.mediaUrl == null) return const SizedBox.shrink();
    
    switch (comment.type) {
      case CommentType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            comment.mediaUrl!,
            height: 150,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stack) => Container(
              height: 150,
              color: Colors.grey[300],
              child: const Icon(Icons.broken_image),
            ),
          ),
        );
      
      case CommentType.video:
        return Container(
          height: 150,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(Icons.play_circle_fill, color: Colors.white, size: 50),
          ),
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildActionButtons(Comment comment) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Row(
        children: [
          // زمن النشر
          Text(
            _formatTime(comment.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // زر الإعجاب
          GestureDetector(
            onTap: () => _toggleLike(comment),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  comment.currentUserReaction != ReactionType.none
                      ? Icons.thumb_up
                      : Icons.thumb_up_outlined,
                  size: 16,
                  color: comment.currentUserReaction != ReactionType.none
                      ? Colors.blue
                      : Colors.grey[600],
                ),
                if (comment.likesCount > 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '${comment.likesCount}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // زر الرد
          GestureDetector(
            onTap: () => widget.onReply(comment),
            child: Text(
              'رد',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepliesSection() {
    return Column(
      children: [
        // زر إظهار/إخفاء الردود
        if (widget.comment.replies.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(right: (widget.comment.depth + 1) * 24.0),
            child: GestureDetector(
              onTap: () => setState(() => _showReplies = !_showReplies),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _showReplies ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _showReplies 
                        ? 'إخفاء الردود'
                        : 'عرض ${widget.comment.totalRepliesCount} رد',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        
        // الردود
        if (_showReplies)
          ...widget.comment.replies.map((reply) => HierarchicalCommentWidget(
            comment: reply.copyWith(depth: widget.comment.depth + 1),
            onReply: widget.onReply,
            onLike: widget.onLike,
            maxDepth: widget.maxDepth,
            showConnectorLines: widget.showConnectorLines,
          )),
      ],
    );
  }

  Future<void> _toggleLike(Comment comment) async {
    if (_isLiking) return;
    setState(() => _isLiking = true);
    
    try {
      // TODO: تنفيذ toggle like للتعليقات
      widget.onLike?.call(comment);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في الإعجاب: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLiking = false);
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }
}

// رسام الخطوط الموصلة مثل فيسبوك
class ConnectorLinesPainter extends CustomPainter {
  final int depth;
  final bool isLast;

  ConnectorLinesPainter({
    required this.depth,
    required this.isLast,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // خط عمودي من الأعلى
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height / 2),
      paint,
    );

    // خط أفقي إلى اليمين
    canvas.drawLine(
      Offset(size.width / 2, size.height / 2),
      Offset(size.width, size.height / 2),
      paint,
    );

    // خط عمودي للأسفل (إذا لم يكن آخر عنصر)
    if (!isLast) {
      canvas.drawLine(
        Offset(size.width / 2, size.height / 2),
        Offset(size.width / 2, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
