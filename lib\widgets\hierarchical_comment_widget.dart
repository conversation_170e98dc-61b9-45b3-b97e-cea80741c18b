import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';

class HierarchicalCommentWidget extends StatefulWidget {
  final Comment comment;
  final Function(Comment) onReply;
  final Function(Comment, ReactionType)? onReaction;
  final int maxDepth;
  final bool showConnectorLines;

  const HierarchicalCommentWidget({
    super.key,
    required this.comment,
    required this.onReply,
    this.onReaction,
    this.maxDepth = 10,
    this.showConnectorLines = true,
  });

  @override
  State<HierarchicalCommentWidget> createState() => _HierarchicalCommentWidgetState();
}

class _HierarchicalCommentWidgetState extends State<HierarchicalCommentWidget> {
  bool _showReplies = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // التعليق الأساسي
        _buildCommentItem(widget.comment),
        
        // الردود مع التسلسل الهرمي
        if (_showReplies && widget.comment.replies.isNotEmpty)
          _buildRepliesSection(),
      ],
    );
  }

  Widget _buildCommentItem(Comment comment) {
    final isReply = comment.depth > 0;
    
    return Container(
      margin: EdgeInsets.only(
        right: comment.depth * 24.0, // إزاحة حسب العمق
        bottom: 8,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // خطوط الاتصال (مثل فيسبوك)
          if (isReply && widget.showConnectorLines)
            _buildConnectorLines(comment),
          
          // صورة المستخدم
          CircleAvatar(
            radius: isReply ? 14 : 18,
            backgroundImage: comment.userAvatar.isNotEmpty
                ? NetworkImage(comment.userAvatar)
                : null,
            backgroundColor: Colors.grey[300],
            child: comment.userAvatar.isEmpty
                ? Icon(
                    Icons.person,
                    size: isReply ? 16 : 20,
                    color: Colors.grey[600],
                  )
                : null,
          ),
          
          const SizedBox(width: 8),
          
          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // فقاعة التعليق
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المستخدم
                      Text(
                        comment.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: isReply ? 13 : 14,
                          color: Colors.blue[800],
                        ),
                      ),
                      
                      // إشارة للمستخدم المُرد عليه
                      if (comment.replyToUserName != null)
                        Text(
                          'رداً على ${comment.replyToUserName}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      
                      const SizedBox(height: 4),
                      
                      // محتوى التعليق
                      Text(
                        comment.content,
                        style: TextStyle(
                          fontSize: isReply ? 13 : 14,
                        ),
                      ),
                      
                      // الميديا إن وجدت
                      if (comment.mediaUrl != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: _buildMediaContent(comment),
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // أزرار الإجراءات
                _buildActionButtons(comment),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConnectorLines(Comment comment) {
    return Container(
      width: 20,
      height: 50,
      margin: const EdgeInsets.only(left: 4, right: 4),
      child: CustomPaint(
        painter: ConnectorLinesPainter(
          depth: comment.depth,
          isLast: false, // يمكن تحسين هذا لاحقاً
        ),
      ),
    );
  }

  Widget _buildMediaContent(Comment comment) {
    if (comment.mediaUrl == null) return const SizedBox.shrink();
    
    switch (comment.type) {
      case CommentType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            comment.mediaUrl!,
            height: 150,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stack) => Container(
              height: 150,
              color: Colors.grey[300],
              child: const Icon(Icons.broken_image),
            ),
          ),
        );
      
      case CommentType.video:
        return Container(
          height: 150,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(Icons.play_circle_fill, color: Colors.white, size: 50),
          ),
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildActionButtons(Comment comment) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Row(
        children: [
          // زمن النشر
          Text(
            _formatTime(comment.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(width: 16),

          // زر التفاعلات (نفس منطق المنشورات)
          if (widget.onReaction != null)
            _buildReactionButton(comment),

          const SizedBox(width: 16),

          // زر الرد
          GestureDetector(
            onTap: () => widget.onReply(comment),
            child: Text(
              'رد',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepliesSection() {
    return Column(
      children: [
        // زر إظهار/إخفاء الردود - يظهر دائماً إذا كان هناك ردود
        if (widget.comment.replies.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(right: (widget.comment.depth + 1) * 24.0, top: 4),
            child: GestureDetector(
              onTap: () => setState(() => _showReplies = !_showReplies),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showReplies ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 14,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _showReplies
                          ? 'إخفاء الردود (${widget.comment.totalRepliesCount})'
                          : 'عرض ${widget.comment.totalRepliesCount} ${widget.comment.totalRepliesCount == 1 ? "رد" : "رد"}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        // الردود
        if (_showReplies)
          ...widget.comment.replies.map((reply) => HierarchicalCommentWidget(
            comment: reply.copyWith(depth: widget.comment.depth + 1),
            onReply: widget.onReply,
            onReaction: widget.onReaction,
            maxDepth: widget.maxDepth,
            showConnectorLines: widget.showConnectorLines,
          )),
      ],
    );
  }



  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  // نفس منطق المنشورات لزر التفاعل
  Widget _buildReactionButton(Comment comment) {
    final hasReaction = comment.currentUserReaction != ReactionType.none;
    final totalReactions = comment.reactionCounts.values.fold<int>(0, (sum, count) => sum + count);

    return GestureDetector(
      onTap: () {
        if (hasReaction) {
          // إزالة التفاعل الحالي
          widget.onReaction!(comment, ReactionType.none);
        } else {
          // إضافة لايك
          widget.onReaction!(comment, ReactionType.like);
        }
      },
      onLongPressStart: (details) async {
        final selected = await _openReactionPickerAt(details.globalPosition, comment);
        if (selected != null) {
          widget.onReaction!(comment, selected);
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة التفاعل
          if (hasReaction)
            Image.asset(
              comment.currentUserReaction.assetPath,
              width: 14,
              height: 14,
              fit: BoxFit.contain,
            )
          else
            Icon(
              Icons.thumb_up_outlined,
              size: 14,
              color: Colors.grey[600],
            ),

          // عدد التفاعلات
          if (totalReactions > 0) ...[
            const SizedBox(width: 3),
            Text(
              '$totalReactions',
              style: TextStyle(
                fontSize: 11,
                color: hasReaction
                    ? comment.currentUserReaction.color
                    : Colors.grey[600],
                fontWeight: hasReaction ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // نفس دالة المنشورات لإظهار التفاعلات
  Future<ReactionType?> _openReactionPickerAt(Offset globalPos, Comment comment) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.support,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.angry,
      ReactionType.sad,
      ReactionType.celebrate,
      ReactionType.insightful,
    ];

    final screenSize = MediaQuery.of(context).size;
    const pickerWidth = 280.0;
    const pickerHeight = 60.0;

    double left = globalPos.dx - (pickerWidth / 2);
    double top = globalPos.dy - pickerHeight - 10;

    if (left < 10) left = 10;
    if (left + pickerWidth > screenSize.width - 10) {
      left = screenSize.width - pickerWidth - 10;
    }
    if (top < 50) top = globalPos.dy + 10;

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          // منطقة شفافة لإغلاق الحوار عند النقر خارجه
          Positioned.fill(
            child: GestureDetector(
              onTap: () => Navigator.pop(ctx),
              child: Container(color: Colors.transparent),
            ),
          ),

          // التفاعلات
          Positioned(
            left: left,
            top: top,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: items.map((reaction) =>
                    GestureDetector(
                      onTap: () => Navigator.pop(ctx, reaction),
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        padding: const EdgeInsets.all(4),
                        child: Image.asset(
                          reaction.assetPath,
                          width: 24,
                          height: 24,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ).toList(),
                ),
              ),
            ),
          ),
        ]);
      },
    );

    return selected;
  }
}

// رسام الخطوط الموصلة مثل فيسبوك
class ConnectorLinesPainter extends CustomPainter {
  final int depth;
  final bool isLast;

  ConnectorLinesPainter({
    required this.depth,
    required this.isLast,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[400]!
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // خط عمودي من الأعلى إلى المنتصف
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height * 0.6),
      paint,
    );

    // خط أفقي من المنتصف إلى اليمين
    canvas.drawLine(
      Offset(size.width / 2, size.height * 0.6),
      Offset(size.width - 2, size.height * 0.6),
      paint,
    );

    // خط عمودي للأسفل (إذا لم يكن آخر عنصر)
    if (!isLast) {
      canvas.drawLine(
        Offset(size.width / 2, size.height * 0.6),
        Offset(size.width / 2, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}


