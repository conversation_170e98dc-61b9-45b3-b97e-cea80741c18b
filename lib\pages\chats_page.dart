import 'package:flutter/material.dart';
import 'chat_page.dart';
import '../models/chat.dart';
import '../supabase_service.dart';

class ChatsPage extends StatefulWidget {
  const ChatsPage({super.key});

  @override
  State<ChatsPage> createState() => _ChatsPageState();
}

class _ChatsPageState extends State<ChatsPage> {
  late final Stream<List<Chat>> _stream = SupabaseService().chatsStream();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: null,
      floatingActionButton: FloatingActionButton(
        heroTag: 'startChat',
        onPressed: _showStartChat,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add_comment_outlined),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 40, 16, 8),
            child: Row(
              children: const [
                Text('الدردشات', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<List<Chat>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                final chats = snapshot.data ?? [];
                if (chats.isEmpty) {
                  return const Center(child: Text('لا توجد محادثات'));
                }
                return ListView.builder(
                  itemCount: chats.length,
                  itemBuilder: (context, index) {
                    final chat = chats[index];
                    return GestureDetector(
                      onLongPress: () async {
                        final action = await showModalBottomSheet<String>(
                          context: context,
                          builder: (_) => SafeArea(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ListTile(
                                  leading: const Icon(Icons.archive),
                                  title: const Text('أرشفة'),
                                  dense: true,
                                  onTap: () => Navigator.pop(context, 'archive'),
                                ),
                                ListTile(
                                  leading: const Icon(Icons.delete),
                                  title: const Text('حذف من القائمة'),
                                  dense: true,
                                  onTap: () => Navigator.pop(context, 'delete'),
                                ),
                                ListTile(
                                  leading: const Icon(Icons.block),
                                  title: const Text('حظر المستخدم'),
                                  dense: true,
                                  onTap: () => Navigator.pop(context, 'block'),
                                ),
                              ],
                            ),
                          ),
                        );

                        if (action == 'archive') {
                          await SupabaseService().archiveChat(chat.id, true);
                        } else if (action == 'delete') {
                          await SupabaseService().deleteChatLocally(chat.id);
                        } else if (action == 'block') {
                          await SupabaseService().blockUser(chat.otherId);
                        }
                      },
                      child: Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundImage: NetworkImage(chat.otherAvatar),
                          ),
                          title: Text(chat.otherName),
                          subtitle: Text(chat.lastMessage),
                          trailing: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (chat.lastAt != null)
                                Text(
                                  _getTimeAgo(chat.lastAt!),
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              if (chat.unreadCount > 0)
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Text(
                                    chat.unreadCount.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          onTap: () async {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ChatPage(
                                  chatId: chat.id,
                                  otherId: chat.otherId,
                                  username: chat.otherName,
                                  avatarUrl: chat.otherAvatar,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getTimeAgo(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Future<void> _showStartChat() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (ctx) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.8,
          minChildSize: 0.4,
          maxChildSize: 0.9,
          builder: (context, scrollController) {
            return FutureBuilder<List<Map<String, dynamic>>>(
              future: SupabaseService().fetchUsers(),
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snap.hasError) {
                  return Center(child: Text('خطأ: ${snap.error}'));
                }
                final users = snap.data ?? [];
                if (users.isEmpty) {
                  return const Center(child: Text('لا يوجد مستخدمون'));
                }
                return ListView.builder(
                  controller: scrollController,
                  itemCount: users.length,
                  itemBuilder: (context, index) {
                    final u = users[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundImage: (u['avatar_url'] as String).isNotEmpty
                            ? NetworkImage(u['avatar_url'])
                            : null,
                        child: (u['avatar_url'] as String).isEmpty
                            ? Text((u['name'] as String).substring(0, 1))
                            : null,
                      ),
                      title: Text(u['name'] as String),
                      onTap: () async {
                        final chatId = await SupabaseService().getOrCreateChat(u['id'] as String);
                        if (!mounted) return;
                        Navigator.pop(context); // Close sheet
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ChatPage(
                              chatId: chatId,
                              otherId: u['id'] as String,
                              username: u['name'] as String,
                              avatarUrl: u['avatar_url'] as String? ?? '',
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
} 