[warning] Unused import: 'widgets/comments_sheet.dart' (C:\Arzawo\arzawo\lib\main.dart:3:8)
[warning] Unused import: 'pages/chat_page.dart' (C:\Arzawo\arzawo\lib\main.dart:5:8)
[warning] Unused import: 'widgets/feed_media.dart' (C:\Arzawo\arzawo\lib\main.dart:13:8)
[warning] Unused import: 'package:url_launcher/url_launcher.dart' (C:\Arzawo\arzawo\lib\main.dart:20:8)
[info] Parameter 'key' could be a super parameter (C:\Arzawo\arzawo\lib\main.dart:72:9)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:115:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:131:49)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:171:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:193:51)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:224:79)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:232:58)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:254:79)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:262:58)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\main.dart:305:43)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\main.dart:313:62)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:473:35)
[info] The private field _currentIndex could be 'final' (C:\Arzawo\arzawo\lib\main.dart:568:7)
[warning] The value of the field '_currentIndex' isn't used (C:\Arzawo\arzawo\lib\main.dart:568:7)
[warning] The value of the field '_pages' isn't used (C:\Arzawo\arzawo\lib\main.dart:569:22)
[warning] The left operand can't be null, so the right operand is never executed (C:\Arzawo\arzawo\lib\main.dart:625:47)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:689:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:711:51)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:742:79)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:750:58)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:771:79)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:779:58)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:801:79)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:809:58)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\main.dart:832:43)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\main.dart:840:62)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:935:29)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\main.dart:955:47)
[warning] Unused import: 'package:flutter/material.dart' (C:\Arzawo\arzawo\lib\models\comment.dart:1:8)
[warning] Unused import: 'package:flutter/material.dart' (C:\Arzawo\arzawo\lib\models\post.dart:1:8)
[warning] Unused import: 'package:flutter/material.dart' (C:\Arzawo\arzawo\lib\models\story.dart:1:8)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\app_settings_page.dart:55:28)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\app_settings_page.dart:126:32)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\app_settings_page.dart:145:32)
[info] The import of 'dart:typed_data' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/services.dart' (C:\Arzawo\arzawo\lib\pages\chat_page.dart:1:8)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\chat_page.dart:214:17)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\chat_page.dart:227:17)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\pages\chat_page.dart:292:48)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\chats_page.dart:221:39)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\chats_page.dart:223:27)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:20:28)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:53:28)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:198:42)
[warning] A value for optional parameter 'key' isn't ever given (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:279:33)
[warning] Unnecessary type check; the result is always 'true' (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:304:11)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\pages\live_stream_setup_page.dart:337:27)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\profile_page.dart:87:29)
[warning] Unused import: 'package:supabase_flutter/supabase_flutter.dart' (C:\Arzawo\arzawo\lib\pages\settings_page.dart:2:8)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\settings_page.dart:54:42)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\settings_page.dart:56:28)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\settings_page.dart:65:42)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\settings_page.dart:67:28)
[info] 'VideoPlayerController.network' is deprecated and shouldn't be used. Use VideoPlayerController.networkUrl instead (C:\Arzawo\arzawo\lib\pages\stories_page.dart:155:19)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\stories_page.dart:346:36)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\pages\stories_page.dart:346:60)
[info] 'VideoPlayerController.network' is deprecated and shouldn't be used. Use VideoPlayerController.networkUrl instead (C:\Arzawo\arzawo\lib\pages\story_viewer_page.dart:92:19)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (C:\Arzawo\arzawo\lib\pages\users_page.dart:162:35)
[info] Statements in an if should be enclosed in a block (C:\Arzawo\arzawo\lib\supabase_service.dart:93:33)
[info] Statements in an if should be enclosed in a block (C:\Arzawo\arzawo\lib\supabase_service.dart:94:16)
[warning] The '!' will have no effect because the receiver can't be null (C:\Arzawo\arzawo\lib\supabase_service.dart:211:90)
[warning] The left operand can't be null, so the right operand is never executed (C:\Arzawo\arzawo\lib\supabase_service.dart:620:25)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (C:\Arzawo\arzawo\lib\widgets\comments_sheet.dart:267:41)
[error] A value of type 'Stream<List<ConnectivityResult>>' can't be assigned to a variable of type 'Stream<ConnectivityResult>' (C:\Arzawo\arzawo\lib\widgets\connectivity_banner.dart:18:15)
[info] The type of the right operand ('ConnectivityResult') isn't a subtype or a supertype of the left operand ('List<ConnectivityResult>') (C:\Arzawo\arzawo\lib\widgets\connectivity_banner.dart:24:84)
[info] 'VideoPlayerController.network' is deprecated and shouldn't be used. Use VideoPlayerController.networkUrl instead (C:\Arzawo\arzawo\lib\widgets\feed_media.dart:21:24)
[warning] Unused import: 'dart:typed_data' (C:\Arzawo\arzawo\lib\widgets\new_post_sheet.dart:2:8)
[info] Don't use 'BuildContext's across async gaps (C:\Arzawo\arzawo\lib\widgets\new_post_sheet.dart:250:28)
[info] Parameter 'key' could be a super parameter (C:\Arzawo\arzawo\lib\widgets\new_post_sheet.dart:262:9)
[info] The import of 'link_preview.dart' is unnecessary because all of the used elements are also provided by the import of 'feed_media.dart' (C:\Arzawo\arzawo\lib\widgets\share_post_sheet.dart:5:8)
[error] The named parameter 'color' is required, but there's no corresponding argument (C:\Arzawo\arzawo\tool\icon_generator.dart:8:7)
[error] The named parameter 'x1' is required, but there's no corresponding argument (C:\Arzawo\arzawo\tool\icon_generator.dart:8:7)
[error] The named parameter 'x2' is required, but there's no corresponding argument (C:\Arzawo\arzawo\tool\icon_generator.dart:8:7)
[error] The named parameter 'y1' is required, but there's no corresponding argument (C:\Arzawo\arzawo\tool\icon_generator.dart:8:7)
[error] The named parameter 'y2' is required, but there's no corresponding argument (C:\Arzawo\arzawo\tool\icon_generator.dart:8:7)
[error] Too many positional arguments: 1 expected, but 6 found (C:\Arzawo\arzawo\tool\icon_generator.dart:8:23)
[info] Don't invoke 'print' in production code (C:\Arzawo\arzawo\tool\icon_generator.dart:15:3)