import 'package:flutter/material.dart';
import 'reaction_type.dart';

enum CommentType { text, image, video }

class Comment {
  final String id;
  final String postId;
  final String? parentId;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final DateTime createdAt;
  final CommentType type;
  final String? mediaUrl;
  List<Comment> replies;
  final int likesCount;
  final ReactionType currentUserReaction;

  Comment({
    required this.id,
    required this.postId,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    required this.createdAt,
    required this.type,
    this.parentId,
    this.mediaUrl,
    this.replies = const [],
    this.likesCount = 0,
    this.currentUserReaction = ReactionType.none,
  });
} 