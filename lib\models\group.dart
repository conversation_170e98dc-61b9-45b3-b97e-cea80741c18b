import 'package:flutter/foundation.dart';

enum GroupPrivacy { public, private, hidden }

class Group {
  final String id;
  final String name;
  final String description;
  final String ownerId;
  final DateTime createdAt;
  final int membersCount;
  final bool joined;
  final String coverUrl;
  final GroupPrivacy privacy;
  final int postsCount;
  final bool isAdmin;

  const Group({
    required this.id,
    required this.name,
    required this.description,
    required this.ownerId,
    required this.createdAt,
    this.coverUrl = '',
    this.privacy = GroupPrivacy.public,
    this.membersCount = 0,
    this.postsCount = 0,
    this.joined = false,
    this.isAdmin = false,
  });

  Group copyWith({
    String? id,
    String? name,
    String? description,
    String? ownerId,
    DateTime? createdAt,
    int? membersCount,
    bool? joined,
    String? coverUrl,
    GroupPrivacy? privacy,
    int? postsCount,
    bool? isAdmin,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      createdAt: createdAt ?? this.createdAt,
      membersCount: membersCount ?? this.membersCount,
      joined: joined ?? this.joined,
      coverUrl: coverUrl ?? this.coverUrl,
      privacy: privacy ?? this.privacy,
      postsCount: postsCount ?? this.postsCount,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }
} 