import 'package:flutter/material.dart';
import '../supabase_service.dart';

class JoinRequestsPage extends StatefulWidget {
  final String groupId;
  const JoinRequestsPage({super.key, required this.groupId});

  @override
  State<JoinRequestsPage> createState() => _JoinRequestsPageState();
}

class _JoinRequestsPageState extends State<JoinRequestsPage> {
  late Future<List<Map<String, dynamic>>> _future;

  @override
  void initState() {
    super.initState();
    _load();
  }

  void _load() {
    setState(() => _future = SupabaseService().fetchJoinRequests(widget.groupId));
  }

  Future<void> _respond(String reqId, bool approve) async {
    await SupabaseService().respondJoinRequest(reqId, approve);
    _load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('طلبات الانضمام')),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _future,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
          final reqs = snapshot.data!;
          if (reqs.isEmpty) return const Center(child: Text('لا توجد طلبات'));
          return RefreshIndicator(
            onRefresh: () async => _load(),
            child: ListView.separated(
              itemCount: reqs.length,
              separatorBuilder: (_, __) => const Divider(height: 1),
              itemBuilder: (ctx, i) {
                final r = reqs[i];
                return ListTile(
                  leading: CircleAvatar(backgroundImage: NetworkImage(r['avatar'] ?? '')),
                  title: Text(r['name'] ?? ''),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.red),
                        onPressed: () => _respond(r['id'], false),
                      ),
                      IconButton(
                        icon: const Icon(Icons.check, color: Colors.green),
                        onPressed: () => _respond(r['id'], true),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
} 