import 'package:flutter/material.dart';
import '../models/post.dart';
import '../supabase_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'settings_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ProfilePage extends StatefulWidget {
  final String userId;
  final String username;

  const ProfilePage({
    super.key,
    required this.userId,
    required this.username,
  });

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Map<String, dynamic>? _profileInfo;
  List<Post> _posts = [];
  bool _isMe = false;
  bool _isFollowing = false;
  bool _loading = true;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    final service = SupabaseService();
    final data = await service.fetchProfile(widget.userId);
    final posts = await service.fetchPostsByUser(widget.userId);
    final following = await service.isFollowing(widget.userId);
    final current = Supabase.instance.client.auth.currentUser?.id;
    setState(() {
      _profileInfo = data ?? {};
      _posts = posts;
      _isMe = current == widget.userId;
      _isFollowing = following;
      _loading = false;
    });
  }

  Future<void> _showEditDialog() async {
    final nameController = TextEditingController(text: _profileInfo?['name'] ?? '');
    final bioController = TextEditingController(text: _profileInfo?['bio'] ?? '');
    String? avatarPath;
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (ctx) => Padding(
        padding: EdgeInsets.only(bottom: MediaQuery.of(ctx).viewInsets.bottom, left:16,right:16,top:16),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          GestureDetector(
            onTap: () async {
              final file = await _picker.pickImage(source: ImageSource.gallery);
              if (file != null) setState(() => avatarPath = file.path);
            },
            child: CircleAvatar(
              radius: 40,
              backgroundImage: avatarPath != null
                  ? FileImage(File(avatarPath!))
                  : NetworkImage(_profileInfo?['avatar_url'] ?? 'https://via.placeholder.com/150') as ImageProvider,
            ),
          ),
          TextField(controller: nameController, decoration: const InputDecoration(labelText:'الاسم')),
          TextField(controller: bioController, decoration: const InputDecoration(labelText:'السيرة الذاتية')),
          const SizedBox(height:12),
          ElevatedButton(
            onPressed: () async {
              String? uploaded;
              if (avatarPath!=null){
                final bytes=await File(avatarPath!).readAsBytes();
                final ext=avatarPath!.split('.').last;
                final url=await SupabaseService().uploadMedia(bytes,'avatars/${widget.userId}.$ext');
                uploaded=url;
              }
              await SupabaseService().updateProfile(name: nameController.text,bio: bioController.text,avatarUrl: uploaded);
              Navigator.pop(ctx);
              _loadProfile();
            }, child: const Text('حفظ'))
        ]),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_profileInfo?['name'] ?? widget.username),
        backgroundColor: Colors.red,
        actions: _isMe
            ? [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _showEditDialog,
                ),
                IconButton(
                  icon: const Icon(Icons.settings),
                  onPressed: () {
                    Navigator.push(context, MaterialPageRoute(builder: (_) => const SettingsPage()));
                  },
                ),
              ]
            : null,
      ),
      body: Column(
        children: [
          // معلومات الملف الشخصي
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundImage: (_profileInfo?['avatar_url'] ?? '').toString().isNotEmpty
                      ? NetworkImage(_profileInfo!['avatar_url'])
                      : const NetworkImage('https://via.placeholder.com/80'),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.username,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildStatColumn('المنشورات', '${_posts.length}'),
                          _buildStatColumn('المتابعون', '${_profileInfo?['followers_count'] ?? 0}'),
                          _buildStatColumn('يتابع', '${_profileInfo?['following_count'] ?? 0}'),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // زر المتابعة
          if (!_isMe)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: () async {
                  final res = await SupabaseService().toggleFollow(widget.userId);
                  setState(() {
                    _isFollowing = res;
                    _profileInfo?['followers_count'] = (_profileInfo?['followers_count'] ?? 0) + (res ? 1 : -1);
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isFollowing ? Colors.grey : Colors.red,
                  minimumSize: const Size(double.infinity, 40),
                ),
                child: Text(
                  _isFollowing ? 'إلغاء المتابعة' : 'متابعة',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
          const SizedBox(height: 16),
          // قائمة المنشورات
          Expanded(
            child: _loading
                ? const Center(child: CircularProgressIndicator())
                : _posts.isEmpty
                    ? const Center(child: Text('لا توجد منشورات'))
                    : ListView.builder(
                        itemCount: _posts.length,
                        itemBuilder: (context, index) {
                          final post = _posts[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(post.content),
                                  if (post.mediaUrl != null) ...[
                                    const SizedBox(height: 8),
                                    post.type == PostType.image
                                        ? Image.network(post.mediaUrl!, fit: BoxFit.cover)
                                        : Container(
                                            height: 200,
                                            decoration: BoxDecoration(color: Colors.black, borderRadius: BorderRadius.circular(8)),
                                            child: const Center(
                                              child: Icon(Icons.play_circle_outline, color: Colors.white, size: 50),
                                            ),
                                          ),
                                  ],
                                ],
                              ),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatColumn(String label, String count) {
    return Expanded(
      child: Column(
        children: [
          Text(
            count,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
} 