import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/services.dart';

import '../supabase_service.dart';
import '../models/message.dart';
import 'call_page.dart';

class ChatPage extends StatefulWidget {
  final String chatId;
  final String otherId;
  final String username;
  final String avatarUrl;

  const ChatPage({
    super.key,
    required this.chatId,
    required this.otherId,
    required this.username,
    required this.avatarUrl,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final TextEditingController _controller = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();
  Message? _editingMsg;
  Message? _replyToMsg;

  bool get _isEditing => _editingMsg != null;

  String get _myId => Supabase.instance.client.auth.currentUser!.id;

  Future<void> _sendText() async {
    final text = _controller.text.trim();
    if (text.isEmpty) return;

    if (_isEditing) {
      await SupabaseService().editMessage(messageId: _editingMsg!.id, newContent: text);
      setState(() => _editingMsg = null);
    } else if (_replyToMsg != null) {
      await SupabaseService().sendReply(chatId: widget.chatId, replyTo: _replyToMsg!.id, content: text);
      setState(() => _replyToMsg = null);
    } else {
      await SupabaseService().sendMessage(
        chatId: widget.chatId,
        content: text,
        type: MediaType.text,
      );
    }

    _controller.clear();
    _scrollToBottom();
  }

  Future<void> _sendImage() async {
    final XFile? img = await _picker.pickImage(source: ImageSource.gallery);
    if (img == null) return;
    final Uint8List bytes = await img.readAsBytes();
    await SupabaseService().sendMessage(
      chatId: widget.chatId,
      content: '',
      type: MediaType.image,
      bytes: bytes,
    );
    _scrollToBottom();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent + 100,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Widget _buildBubble(Message msg) {
    final bool isMe = msg.senderId == _myId;
    final Color bubbleColor = isMe ? Colors.red : Colors.grey.shade200;
    final TextStyle textStyle = TextStyle(color: isMe ? Colors.white : Colors.black);

    Widget content;
    if (msg.type == MediaType.text) {
      content = Text(msg.content, style: textStyle);
    } else if (msg.type == MediaType.image && msg.mediaUrl != null) {
      content = ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(msg.mediaUrl!, fit: BoxFit.cover),
      );
    } else if (msg.type == MediaType.audio && msg.mediaUrl != null) {
      content = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.play_arrow, color: Colors.white),
          const SizedBox(width: 4),
          const Text('مقطع صوتي', style: TextStyle(color: Colors.white)),
        ],
      );
    } else {
      content = Text('رسالة غير مدعومة', style: textStyle);
    }

    return GestureDetector(
      onLongPress: () => _onMessageLongPress(msg, isMe),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!isMe) ...[
              CircleAvatar(radius: 16, backgroundImage: NetworkImage(widget.avatarUrl)),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: bubbleColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onMessageLongPress(Message msg, bool isMe) async {
    final action = await showModalBottomSheet<String>(
      context: context,
      builder: (_) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isMe)
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تعديل'),
                onTap: () => Navigator.pop(context, 'edit'),
              ),
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('رد'),
              onTap: () => Navigator.pop(context, 'reply'),
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('نسخ'),
              onTap: () => Navigator.pop(context, 'copy'),
            ),
          ],
        ),
      ),
    );

    if (action == null) return;
    switch (action) {
      case 'edit':
        setState(() {
          _editingMsg = msg;
          _controller.text = msg.content;
          _controller.selection = TextSelection.collapsed(offset: _controller.text.length);
        });
        break;
      case 'reply':
        setState(() => _replyToMsg = msg);
        break;
      case 'copy':
        await Clipboard.setData(ClipboardData(text: msg.content));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final stream = SupabaseService().messagesStream(widget.chatId);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.red,
        title: Row(
          children: [
            CircleAvatar(backgroundImage: NetworkImage(widget.avatarUrl), radius: 18),
            const SizedBox(width: 8),
            Text(widget.username),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: _onChatMenuSelected,
            itemBuilder: (_) => [
              const PopupMenuItem(value: 'block',    child: Text('حظر المستخدم')),
              const PopupMenuItem(value: 'unblock',  child: Text('إلغاء الحظر')),
              const PopupMenuItem(value: 'report',   child: Text('الإبلاغ عن محادثة')),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.video_call),
            onPressed: () async {
              final callId = await SupabaseService().createCall(chatId: widget.chatId, type: 'video');
              if (!mounted) return;
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => CallPage(callId: callId, otherName: widget.username, type: 'video'),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () async {
              final callId = await SupabaseService().createCall(chatId: widget.chatId, type: 'voice');
              if (!mounted) return;
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => CallPage(callId: callId, otherName: widget.username, type: 'voice'),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: StreamBuilder<List<Message>>(
              stream: stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                final List<Message> msgs = snapshot.data ?? [];
                // علّم الرسائل كمقروءة عند وصولها
                if (msgs.isNotEmpty) {
                  SupabaseService().markMessagesRead(widget.chatId);
                }
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  itemCount: msgs.length,
                  itemBuilder: (context, index) => _buildBubble(msgs[index]),
                );
              },
            ),
          ),
          if (_replyToMsg != null)
            Container(
              color: Colors.grey.shade100,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  const Icon(Icons.reply, color: Colors.red, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      _replyToMsg!.content,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(color: Colors.black87),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: () => setState(() => _replyToMsg = null),
                  ),
                ],
              ),
            ),
          SafeArea(
            top: false,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(color: Colors.grey.withOpacity(0.2), blurRadius: 3),
                ],
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.image),
                    color: Colors.red,
                    onPressed: _sendImage,
                  ),
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      decoration: const InputDecoration(
                        hintText: 'اكتب رسالة...',
                        border: InputBorder.none,
                      ),
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send),
                    color: Colors.red,
                    onPressed: _sendText,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _onChatMenuSelected(String value) async {
    switch (value) {
      case 'block':
        await SupabaseService().blockUser(widget.otherId);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حظر المستخدم')));
        break;
      case 'unblock':
        await SupabaseService().unblockUser(widget.otherId);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إلغاء الحظر')));
        break;
      case 'report':
        final reason = await _askReason();
        if (reason != null) {
          await SupabaseService().reportChat(widget.chatId, reason);
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إرسال البلاغ')));
        }
        break;
    }
  }

  Future<String?> _askReason() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('سبب الإبلاغ'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(hintText: 'اكتب السبب هنا'),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
          ElevatedButton(onPressed: () => Navigator.pop(context, controller.text.trim()), child: const Text('إرسال')),
        ],
      ),
    );
  }
} 