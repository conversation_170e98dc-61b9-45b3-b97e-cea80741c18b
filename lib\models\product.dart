import 'package:flutter/material.dart';

// ------------------ Enums ------------------ //

enum ProductCondition { newItem, used }

enum SellerType { individual, merchant }

enum DeliveryMethod { delivery, pickup }

enum ContactMethod { phone, inAppChat }

enum PaymentChannel { cashOnDelivery, bankTransfer, other }

// ------------------ Sub-models ------------------ //

class ProductImage {
  final String url;
  final int index;
  const ProductImage({required this.url, required this.index});
}

// ------------------ Product ------------------ //

class Product {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final List<ProductImage> images;
  final String name;
  final String description;
  final double price;
  final bool negotiable;
  final String category;
  final String? brand;
  final String country;
  final String currency;
  final String city;
  final String? address;
  final ContactMethod contactMethod;
  final String? phone;
  final ProductCondition condition;
  final int quantity;
  final SellerType sellerType;
  final DeliveryMethod deliveryMethod;
  final double? deliveryCost;
  final List<PaymentChannel> paymentMethods;
  final DateTime createdAt;

  // social counters (local, may be updated live)
  int viewsCount;
  int likesCount;
  int commentsCount;
  int sharesCount;
  bool likedByMe;

  Product({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.images,
    required this.name,
    required this.description,
    required this.price,
    required this.negotiable,
    required this.category,
    this.brand,
    required this.country,
    required this.currency,
    required this.city,
    this.address,
    required this.contactMethod,
    this.phone,
    required this.condition,
    required this.quantity,
    required this.sellerType,
    required this.deliveryMethod,
    this.deliveryCost,
    required this.paymentMethods,
    required this.createdAt,
    required this.viewsCount,
    required this.likesCount,
    required this.commentsCount,
    required this.sharesCount,
    this.likedByMe = false,
  });
} 