import 'dart:io';
import 'package:image/image.dart' as img;

void main() {
  const size = 1024; // create high-res square icon

  final image = img.Image(width: size, height: size);
  img.fillRect(image, x1: 0, y1: 0, x2: size, y2: size, color: img.ColorRgb8(198, 40, 40));

  // (اختياري) يمكن رسم الحرف "a" لاحقًا، الآن نكتفي بالمربع الأحمر حتى لا نتسبب في أخطاء.

  final png = img.encodePng(image);
  final outFile = File('assets/app_icon.png')..createSync(recursive: true);
  outFile.writeAsBytesSync(png);
  print('Generated assets/app_icon.png');
} 