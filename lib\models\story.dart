import 'package:flutter/material.dart';

enum StoryType { text, image, video }

class Story {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final StoryType type;
  final String? text;
  final String? mediaUrl;
  final DateTime createdAt;
  final DateTime expiresAt;

  Story({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.type,
    this.text,
    this.mediaUrl,
    required this.createdAt,
    required this.expiresAt,
  });
} 