import 'package:flutter/material.dart';

// Expanded reaction types for modern stickers (LinkedIn-style)
enum ReactionType {
  like('like'),
  dislike('dislike'),
  celebrate('celebrate'),
  support('support'),
  love('love'),
  funny('funny'),
  angry('angry'),
  sad('sad'),
  insightful('insightful'),
  none('none');

  final String value;
  const ReactionType(this.value);

  static ReactionType fromString(String? v) {
    if (v == null) return ReactionType.none;
    return ReactionType.values.firstWhere(
      (rt) => rt.value == v,
      orElse: () => ReactionType.none,
    );
  }
}

// خصائص الأيقونات والألوان لكل تفاعل
extension ReactionVisuals on ReactionType {
  // استخدام أيقونات PNG بدلاً من Material Design icons
  String get assetPath {
    switch (this) {
      case ReactionType.like:
        return 'assets/reactions/Like.png';
      case ReactionType.dislike:
        return 'assets/reactions/Dislike.png';
      case ReactionType.celebrate:
        return 'assets/reactions/Like.png'; // يمكن استخدام أيقونة مخصصة للاحتفال
      case ReactionType.support:
        return 'assets/reactions/Support.png';
      case ReactionType.love:
        return 'assets/reactions/Love.png';
      case ReactionType.funny:
        return 'assets/reactions/Haha.png';
      case ReactionType.angry:
        return 'assets/reactions/Angry.png';
      case ReactionType.sad:
        return 'assets/reactions/Sad.png';
      case ReactionType.insightful:
        return 'assets/reactions/Like.png'; // يمكن استخدام أيقونة مخصصة للبصيرة
      default:
        return 'assets/reactions/Like.png';
    }
  }

  // احتفظ بـ IconData كـ fallback للحالات التي قد تحتاجها
  IconData get icon {
    switch (this) {
      case ReactionType.like:
        return Icons.thumb_up_alt_rounded;
      case ReactionType.dislike:
        return Icons.thumb_down_alt_rounded;
      case ReactionType.celebrate:
        return Icons.emoji_events_rounded;
      case ReactionType.support:
        return Icons.volunteer_activism_rounded;
      case ReactionType.love:
        return Icons.favorite_rounded;
      case ReactionType.funny:
        return Icons.sentiment_very_satisfied_rounded;
      case ReactionType.angry:
        return Icons.sentiment_very_dissatisfied_rounded;
      case ReactionType.sad:
        return Icons.sentiment_dissatisfied_rounded;
      case ReactionType.insightful:
        return Icons.lightbulb_rounded;
      default:
        return Icons.thumb_up_alt_rounded;
    }
  }

  Color get color {
    switch (this) {
      case ReactionType.like:
        return Colors.blueAccent;
      case ReactionType.dislike:
        return Colors.red;
      case ReactionType.celebrate:
        return Colors.amber;
      case ReactionType.support:
        return Colors.teal;
      case ReactionType.love:
        return Colors.redAccent;
      case ReactionType.funny:
        return Colors.orangeAccent;
      case ReactionType.angry:
        return Colors.deepOrange;
      case ReactionType.sad:
        return Colors.blueGrey;
      case ReactionType.insightful:
        return Colors.yellow.shade700;
      default:
        return Colors.grey;
    }
  }

}