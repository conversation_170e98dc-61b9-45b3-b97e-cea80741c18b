import 'package:flutter/material.dart';
import '../models/post.dart';
import '../supabase_service.dart';
import 'feed_media.dart';
import 'link_preview.dart';
import 'package:share_plus/share_plus.dart';

class SharePostSheet extends StatefulWidget {
  final Post post;
  final VoidCallback? onShared;

  const SharePostSheet({
    super.key,
    required this.post,
    this.onShared,
  });

  @override
  State<SharePostSheet> createState() => _SharePostSheetState();
}

class _SharePostSheetState extends State<SharePostSheet> {
  final TextEditingController _thoughtController = TextEditingController();
  bool _isQuickShare = true;
  bool _sending = false;

  Future<void> _sharePost() async {
    if (_sending) return;
    setState(() => _sending = true);
    try {
      await SupabaseService().createPost(
        content: _thoughtController.text,
        type: PostType.shared,
        sharedPostId: widget.post.id,
      );
      widget.onShared?.call();
      if (mounted) Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل إعادة النشر: $e')),
        );
      }
    }
    if (mounted) setState(() => _sending = false);
  }

  void _shareExternal() {
    final text = '${widget.post.content}\n\nhttps://arzawo.com/posts/${widget.post.id}';
    Share.share(text);
  }

  @override
  void dispose() {
    _thoughtController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom + 8,
        top: 16,
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text(
                'مشاركة المنشور',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  setState(() {
                    _isQuickShare = !_isQuickShare;
                  });
                },
                child: Text(
                  _isQuickShare ? 'إضافة فكرة' : 'مشاركة سريعة',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (!_isQuickShare) ...[
            TextField(
              controller: _thoughtController,
              decoration: InputDecoration(
                hintText: 'أضف فكرتك حول هذا المنشور...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
          ],
          Card(
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.red[300],
                        child: Text(widget.post.userName[0]),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.post.userName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'منذ ${DateTime.now().difference(widget.post.createdAt).inMinutes} دقيقة',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.post.content,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (widget.post.type == PostType.link && widget.post.linkUrl != null) ...[
                    const SizedBox(height: 8),
                    LinkPreview(url: widget.post.linkUrl!, meta: widget.post.linkMeta),
                  ] else if (widget.post.mediaUrl != null) ...[
                    const SizedBox(height: 8),
                    if (widget.post.type == PostType.image)
                      FeedImage(url: widget.post.mediaUrl!)
                    else if (widget.post.type == PostType.video)
                      FeedVideoPlayer(url: widget.post.mediaUrl!)
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _sharePost,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _sending ? Colors.grey : Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: _sending
                      ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2,color: Colors.white))
                      : Text(_isQuickShare ? 'مشاركة للمتابعين' : 'نشر'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareExternal,
                  icon: const Icon(Icons.share_outlined),
                  label: const Text('مشاركة خارجية'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}