import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../main.dart';

class AppSettingsPage extends StatefulWidget {
  const AppSettingsPage({super.key});

  @override
  State<AppSettingsPage> createState() => _AppSettingsPageState();
}

class _AppSettingsPageState extends State<AppSettingsPage> {
  bool notifyFollow = true;
  bool notifyLike = true;
  bool notifyComment = true;
  bool notifyChat = true;
  bool notifyApp = true;

  String defaultVisibility = 'public';
  String commentsFrom = 'everyone';

  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final s = await SupabaseService().fetchSettings();
    setState(() {
      notifyFollow = s['notify_follow'] ?? true;
      notifyLike = s['notify_like'] ?? true;
      notifyComment = s['notify_comment'] ?? true;
      notifyChat = s['notify_chat'] ?? true;
      notifyApp = s['notify_app'] ?? true;
      defaultVisibility = s['default_visibility'] ?? 'public';
      commentsFrom = s['comments_from'] ?? 'everyone';
      _loading = false;
    });
  }

  Future<void> _save() async {
    await SupabaseService().updateSettings({
      'notify_follow': notifyFollow,
      'notify_like': notifyLike,
      'notify_comment': notifyComment,
      'notify_chat': notifyChat,
      'notify_app': notifyApp,
      'default_visibility': defaultVisibility,
      'comments_from': commentsFrom,
    });
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حفظ الإعدادات')));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) return const Center(child: CircularProgressIndicator());
    return Scaffold(
      appBar: AppBar(title: const Text('إعدادات التطبيق')),
      body: ListView(
        children: [
          const ListTile(title: Text('الخصوصية', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            title: const Text('من يمكنه رؤية منشوراتى الجديدة؟'),
            trailing: DropdownButton<String>(
              value: defaultVisibility,
              items: const [
                DropdownMenuItem(value: 'public', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابعون فقط')),
                DropdownMenuItem(value: 'private', child: Text('أنا فقط')),
              ],
              onChanged: (v) => setState(() => defaultVisibility = v!),
            ),
          ),
          ListTile(
            title: const Text('من يمكنه التعليق على منشوراتى؟'),
            trailing: DropdownButton<String>(
              value: commentsFrom,
              items: const [
                DropdownMenuItem(value: 'everyone', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابعون فقط')),
              ],
              onChanged: (v) => setState(() => commentsFrom = v!),
            ),
          ),
          const Divider(),
          const ListTile(title: Text('الإشعارات', style: TextStyle(fontWeight: FontWeight.bold))),
          SwitchListTile(title: const Text('إشعار المتابعين'), value: notifyFollow, onChanged: (v)=>setState(()=>notifyFollow=v)),
          SwitchListTile(title: const Text('إشعار الإعجابات'), value: notifyLike, onChanged: (v)=>setState(()=>notifyLike=v)),
          SwitchListTile(title: const Text('إشعار التعليقات'), value: notifyComment, onChanged: (v)=>setState(()=>notifyComment=v)),
          SwitchListTile(title: const Text('إشعار الدردشة'), value: notifyChat, onChanged: (v)=>setState(()=>notifyChat=v)),
          SwitchListTile(title: const Text('إشعارات التطبيق العامة'), value: notifyApp, onChanged: (v)=>setState(()=>notifyApp=v)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal:16),
            child: ElevatedButton(onPressed: _save, child: const Text('حفظ')),
          ),
          const Divider(),
          const ListTile(title: Text('الحساب', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('تسجيل الخروج'),
            onTap: () async {
              await SupabaseService().signOut();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const LoginPage()),
                  (route) => false,
                );
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.pause_circle_filled, color: Colors.orange),
            title: const Text('تعطيل الحساب مؤقتًا'),
            onTap: () async {
              final ok = await _confirm('تعطيل الحساب؟', 'سيتم تعطيل حسابك ولن يظهر فى البحث. يمكنك إعادة تفعيله بتسجيل الدخول لاحقًا.');
              if (ok) {
                await SupabaseService().deactivateAccount();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تعطيل الحساب')));
                  await SupabaseService().signOut();
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (_) => const LoginPage()),
                    (route) => false,
                  );
                }
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete_forever, color: Colors.red),
            title: const Text('حذف الحساب نهائيًا'),
            subtitle: const Text('سيتم جدولة حذف حسابك بعد 30 يومًا'),
            onTap: () async {
              final ok = await _confirm('حذف الحساب؟', 'سيتم حذف حسابك وجميع بياناتك نهائيًا بعد 30 يومًا.');
              if (ok) {
                await SupabaseService().deleteAccount();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم جدولة حذف الحساب بعد 30 يومًا')));
                  await SupabaseService().signOut();
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (_) => const LoginPage()),
                    (route) => false,
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }

  Future<bool> _confirm(String title, String message) async {
    final res = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('تأكيد')),
        ],
      ),
    );
    return res == true;
  }
} 