import 'dart:typed_data';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/product.dart';
import '../supabase_service.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameCtrl = TextEditingController();
  final _descCtrl = TextEditingController();
  final _priceCtrl = TextEditingController();
  final _brandCtrl = TextEditingController();
  final _addressCtrl = TextEditingController();
  final _phoneCtrl = TextEditingController();
  final _quantityCtrl = TextEditingController(text: '1');
  final _cityCtrl = TextEditingController();

  bool _negotiable = false;
  String _category = 'إلكترونيات';
  String _country = 'السعودية';
  String _currency = 'SAR';
  ProductCondition _condition = ProductCondition.newItem;
  SellerType _sellerType = SellerType.individual;
  DeliveryMethod _deliveryMethod = DeliveryMethod.delivery;
  final List<PaymentChannel> _payments = [PaymentChannel.cashOnDelivery];
  ContactMethod _contactMethod = ContactMethod.inAppChat;

  final List<XFile> _pickedFiles = [];
  bool _submitting = false;

  final _categories = [
    'إلكترونيات',
    'ملابس',
    'سيارات',
    'عقارات',
    'أثاث',
    'أجهزة منزلية',
    'كتب',
    'رياضة',
    'موبايلات',
    'كمبيوترات',
    'مكياج',
    'عطور',
    'مجوهرات',
    'خدمات',
    'حيوانات أليفة',
    'ألعاب فيديو',
    'موسيقى',
    'فن',
    'تذاكر',
    'دراجات',
    'معدات صناعية',
    'آلات تصوير',
    'ساعات',
    'أطعمة',
    'مستلزمات طبية',
    'أثاث مكتبي',
    'قرطاسية',
    'مستلزمات أطفال',
    'مستلزمات زراعية',
    'معدات البناء',
    'أجهزة ألعاب',
    'إكسسوارات سيارات',
    'خيم ورحلات',
    'معدات بحرية',
    'أدوات موسيقية',
    'برامج',
    'ألعاب أطفال',
    'معدات كهربائية',
    'دراجات نارية',
    'مزارع',
    'تصميم وخدمات',
    'استشارات',
    'شحن وتوصيل',
    'تطوير مواقع',
    'تسويق',
    'تعليم ودورات',
    'وظائف',
    'مناسبات',
    'أخرى'
  ];

  final _countries = [
    'السعودية',
    'الإمارات',
    'مصر',
    'المغرب',
    'الجزائر',
    'قطر',
    'الكويت',
    'عمان',
    'البحرين',
    'تونس',
    'الأردن',
    'لبنان',
    'سوريا',
    'اليمن',
    'العراق',
    'السودان',
    'ليبيا',
    'تركيا',
    'أمريكا',
    'كندا',
    'بريطانيا',
    'فرنسا',
    'ألمانيا',
    'إسبانيا',
    'إيطاليا',
    'الهند',
    'باكستان',
    'إندونيسيا',
    'ماليزيا',
    'نيجيريا',
    'جنوب أفريقيا',
    'روسيا',
    'الصين',
    'اليابان',
    'كوريا الجنوبية',
    'أستراليا',
    'البرازيل',
    'الأرجنتين',
    'المكسيك',
    'الدنمارك',
    'السويد',
    'النرويج',
    'هولندا',
    'بلجيكا',
    'سويسرا',
    'اليونان',
    'البرتغال',
    'بولندا',
    'تشيك',
  ];

  final _currencyMap = {
    'SAR': 'ريال سعودي',
    'AED': 'درهم إماراتي',
    'EGP': 'جنيه مصري',
    'MAD': 'درهم مغربي',
    'USD': 'دولار أمريكي',
    'EUR': 'يورو',
    'GBP': 'جنيه إسترليني',
    'JPY': 'ين ياباني',
    'CNY': 'يوان صيني',
    'INR': 'روبية هندية',
    'PKR': 'روبية باكستانية',
    'TRY': 'ليرة تركية',
    'DZD': 'دينار جزائري',
    'QAR': 'ريال قطري',
    'KWD': 'دينار كويتي',
    'OMR': 'ريال عماني',
    'BHD': 'دينار بحريني',
    'TND': 'دينار تونسي',
    'LBP': 'ليرة لبنانية',
    'SYP': 'ليرة سورية',
    'YER': 'ريال يمني',
    'SDG': 'جنيه سوداني',
    'LYD': 'دينار ليبي',
    'CAD': 'دولار كندي',
    'AUD': 'دولار أسترالي',
    'BRL': 'ريال برازيلي',
    'MXN': 'بيزو مكسيكي',
    'ZAR': 'راند جنوب أفريقي',
    'RUB': 'روبل روسي',
    'KRW': 'وون كوري',
    'SEK': 'كرونا سويدية',
    'NOK': 'كرونا نرويجية',
    'DKK': 'كرونا دنماركية',
    'PLN': 'زلوتي بولندي',
    'CHF': 'فرنك سويسري',
    'SGD': 'دولار سنغافوري',
    'MYR': 'رينغيت ماليزي',
    'IDR': 'روبية إندونيسية',
    'NGN': 'نايرا نيجيرية',
    'ARS': 'بيزو أرجنتيني',
    'CLP': 'بيزو شيلي',
    'COP': 'بيزو كولومبي',
    'THB': 'بات تايلندي',
    'HKD': 'دولار هونغ كونغ',
    'ILS': 'شيكل إسرائيلي',
    'VND': 'دونغ فيتنامي',
    'IRR': 'ريال إيراني',
    'AFN': 'أفغاني',
    'BDT': 'تاكا بنغلاديشي',
    'PHP': 'بيزو فلبيني',
  };

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final imgs = await picker.pickMultiImage(imageQuality: 80);
    if (imgs != null && imgs.isNotEmpty) {
      setState(() {
        _pickedFiles
          ..clear()
          ..addAll(imgs.take(6)); // حد أقصى 6
      });
    }
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;
    if (_pickedFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('أضف صورة واحدة على الأقل')));
      return;
    }

    setState(() => _submitting = true);

    try {
      final bytesList = <Uint8List>[];
      final extList = <String>[];
      for (final f in _pickedFiles) {
        bytesList.add(await f.readAsBytes());
        extList.add(f.path.split('.').last);
      }

      await SupabaseService().createProduct(
        name: _nameCtrl.text.trim(),
        description: _descCtrl.text.trim(),
        price: double.parse(_priceCtrl.text),
        negotiable: _negotiable,
        category: _category,
        brand: _brandCtrl.text.trim().isEmpty ? null : _brandCtrl.text.trim(),
        country: _country,
        currency: _currency,
        city: _cityCtrl.text.trim(),
        address: _addressCtrl.text.trim().isEmpty ? null : _addressCtrl.text.trim(),
        contactMethod: _contactMethod,
        phone: _contactMethod == ContactMethod.phone ? _phoneCtrl.text.trim() : null,
        condition: _condition,
        quantity: int.tryParse(_quantityCtrl.text) ?? 1,
        sellerType: _sellerType,
        deliveryMethod: _deliveryMethod,
        deliveryCost: null, // يمكن إضافة حقل لاحقاً
        paymentMethods: _payments,
        imagesBytes: bytesList,
        imagesExt: extList,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نشر المنتج')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ: $e')));
    } finally {
      if (mounted) setState(() => _submitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إضافة منتج')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صور
              TextButton.icon(
                onPressed: _pickImages,
                icon: const Icon(Icons.photo_library),
                label: const Text('اختيار صور (حتى 6)'),
              ),
              if (_pickedFiles.isNotEmpty)
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _pickedFiles.length,
                    itemBuilder: (ctx, i) => Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Stack(
                        children: [
                          Image.file(
                            File(_pickedFiles[i].path),
                            width: 100,
                            height: 100,
                            fit: BoxFit.cover,
                          ),
                          Positioned(
                            top: 2,
                            left: 2,
                            child: GestureDetector(
                              onTap: () => setState(() => _pickedFiles.removeAt(i)),
                              child: Container(
                                decoration: BoxDecoration(color: Colors.black45, borderRadius: BorderRadius.circular(12)),
                                child: const Icon(Icons.close, color: Colors.white, size: 18),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              const SizedBox(height: 12),
              // اسم المنتج
              TextFormField(
                controller: _nameCtrl,
                decoration: const InputDecoration(labelText: 'اسم المنتج'),
                validator: (v) => v == null || v.isEmpty ? 'مطلوب' : null,
              ),
              const SizedBox(height: 12),
              // وصف
              TextFormField(
                controller: _descCtrl,
                decoration: const InputDecoration(labelText: 'الوصف التفصيلي'),
                maxLines: 5,
                validator: (v) => v == null || v.isEmpty ? 'مطلوب' : null,
              ),
              const SizedBox(height: 12),
              // السعر
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _priceCtrl,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      decoration: const InputDecoration(labelText: 'السعر'),
                      validator: (v) => v == null || v.isEmpty ? 'مطلوب' : null,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _currency,
                      items: _currencyMap.keys
                          .map((code) => DropdownMenuItem(value: code, child: Text('${_currencyMap[code]} ($code)')))
                          .toList(),
                      onChanged: (v) => setState(() => _currency = v!),
                      decoration: const InputDecoration(labelText: 'العملة'),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  const Text('قابل للتفاوض؟'),
                  Switch(value: _negotiable, onChanged: (v) => setState(() => _negotiable = v)),
                ],
              ),
              const SizedBox(height: 12),
              // الفئة
              DropdownButtonFormField<String>(
                value: _category,
                items: _categories.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                onChanged: (v) => setState(() => _category = v!),
                decoration: const InputDecoration(labelText: 'الفئة'),
              ),
              const SizedBox(height: 12),
              // الماركة
              TextFormField(
                controller: _brandCtrl,
                decoration: const InputDecoration(labelText: 'الماركة/الشركة المصنعة (اختياري)'),
              ),
              const SizedBox(height: 12),
              // الدولة
              DropdownButtonFormField<String>(
                value: _country,
                items: _countries.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                onChanged: (v) => setState(() => _country = v!),
                decoration: const InputDecoration(labelText: 'الدولة/البلد'),
              ),
              const SizedBox(height: 12),
              // المدينة (إدخال يدوي)
              TextFormField(
                controller: _cityCtrl,
                decoration: const InputDecoration(labelText: 'المدينة'),
                validator: (v) => v == null || v.isEmpty ? 'مطلوب' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _addressCtrl,
                decoration: const InputDecoration(labelText: 'العنوان الدقيق (اختياري)'),
              ),
              const SizedBox(height: 12),
              // وسيلة التواصل
              DropdownButtonFormField<ContactMethod>(
                value: _contactMethod,
                items: ContactMethod.values
                    .map((m) => DropdownMenuItem(value: m, child: Text(m == ContactMethod.phone ? 'رقم الهاتف' : 'مراسلة داخل التطبيق')))
                    .toList(),
                onChanged: (v) => setState(() => _contactMethod = v!),
                decoration: const InputDecoration(labelText: 'وسيلة التواصل'),
              ),
              if (_contactMethod == ContactMethod.phone)
                TextFormField(
                  controller: _phoneCtrl,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(labelText: 'رقم الهاتف'),
                  validator: (v) => _contactMethod == ContactMethod.phone && (v == null || v.isEmpty) ? 'مطلوب' : null,
                ),
              const SizedBox(height: 12),
              // حالة المنتج
              DropdownButtonFormField<ProductCondition>(
                value: _condition,
                items: ProductCondition.values
                    .map((c) => DropdownMenuItem(value: c, child: Text(c == ProductCondition.newItem ? 'جديد' : 'مستعمل')))
                    .toList(),
                onChanged: (v) => setState(() => _condition = v!),
                decoration: const InputDecoration(labelText: 'حالة المنتج'),
              ),
              const SizedBox(height: 12),
              // الكمية
              TextFormField(
                controller: _quantityCtrl,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(labelText: 'الكمية'),
              ),
              const SizedBox(height: 12),
              // نوع البائع
              DropdownButtonFormField<SellerType>(
                value: _sellerType,
                items: SellerType.values
                    .map((s) => DropdownMenuItem(value: s, child: Text(s == SellerType.individual ? 'فرد' : 'تاجر')))
                    .toList(),
                onChanged: (v) => setState(() => _sellerType = v!),
                decoration: const InputDecoration(labelText: 'نوع البائع'),
              ),
              const SizedBox(height: 12),
              // طريقة التوصيل
              DropdownButtonFormField<DeliveryMethod>(
                value: _deliveryMethod,
                items: DeliveryMethod.values
                    .map((d) => DropdownMenuItem(value: d, child: Text(d == DeliveryMethod.delivery ? 'توصيل' : 'استلام يدوي')))
                    .toList(),
                onChanged: (v) => setState(() => _deliveryMethod = v!),
                decoration: const InputDecoration(labelText: 'طريقة التوصيل'),
              ),
              const SizedBox(height: 12),
              // وسائل الدفع المقبولة (بسيط: اختيار واحد حاليًا)
              DropdownButtonFormField<PaymentChannel>(
                value: _payments.first,
                items: PaymentChannel.values
                    .map((p) {
                  String t;
                  switch (p) {
                    case PaymentChannel.bankTransfer:
                      t = 'تحويل بنكي';
                      break;
                    case PaymentChannel.other:
                      t = 'أخرى';
                      break;
                    default:
                      t = 'نقدًا عند الاستلام';
                  }
                  return DropdownMenuItem(value: p, child: Text(t));
                }).toList(),
                onChanged: (v) => setState(() {
                  _payments.clear();
                  _payments.add(v!);
                }),
                decoration: const InputDecoration(labelText: 'وسيلة الدفع'),
              ),
              const SizedBox(height: 20),
              _submitting
                  ? const Center(child: CircularProgressIndicator())
                  : SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _submit,
                        child: const Text('نشر المنتج'),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
} 