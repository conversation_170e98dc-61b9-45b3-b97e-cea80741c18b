-- إعداد أمان التعليقات والإبلاغ والحظر
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- 1. جدول الإبلاغ عن التعليقات
CREATE TABLE IF NOT EXISTS comment_reports (
    id BIGSERIAL PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    reporter_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reported_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL CHECK (reason IN ('spam', 'harassment', 'inappropriate', 'fake', 'violence', 'other')),
    description TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by <PERSON>UI<PERSON> REFERENCES auth.users(id),
    
    -- منع الإبلاغ المتكرر من نفس المستخدم على نفس التعليق
    UNIQUE(comment_id, reporter_id)
);

-- 2. جدول حظر المستخدمين (للتعليقات)
CREATE TABLE IF NOT EXISTS comment_blocks (
    id BIGSERIAL PRIMARY KEY,
    blocker_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    blocked_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع الحظر المتكرر
    UNIQUE(blocker_id, blocked_id),
    
    -- منع المستخدم من حظر نفسه
    CHECK (blocker_id != blocked_id)
);

-- 3. إضافة أعمدة للتعليقات
ALTER TABLE comments 
ADD COLUMN IF NOT EXISTS edited_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS edit_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS deleted_by UUID REFERENCES auth.users(id);

-- 4. فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_comment_reports_comment_id ON comment_reports(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_reports_reporter_id ON comment_reports(reporter_id);
CREATE INDEX IF NOT EXISTS idx_comment_reports_status ON comment_reports(status);
CREATE INDEX IF NOT EXISTS idx_comment_blocks_blocker_id ON comment_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_comment_blocks_blocked_id ON comment_blocks(blocked_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_is_deleted ON comments(is_deleted);

-- 5. دالة للإبلاغ عن تعليق
CREATE OR REPLACE FUNCTION report_comment(
    p_comment_id UUID,
    p_reason TEXT,
    p_description TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    reporter_id UUID;
    reported_user_id UUID;
    existing_report comment_reports%ROWTYPE;
    result JSON;
BEGIN
    -- الحصول على معرف المبلغ
    reporter_id := auth.uid();
    IF reporter_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User not authenticated');
    END IF;
    
    -- الحصول على معرف صاحب التعليق
    SELECT user_id INTO reported_user_id 
    FROM comments 
    WHERE id = p_comment_id AND is_deleted = FALSE;
    
    IF reported_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'Comment not found');
    END IF;
    
    -- منع الإبلاغ عن النفس
    IF reporter_id = reported_user_id THEN
        RETURN json_build_object('success', false, 'error', 'Cannot report your own comment');
    END IF;
    
    -- فحص وجود إبلاغ سابق
    SELECT * INTO existing_report 
    FROM comment_reports 
    WHERE comment_id = p_comment_id AND reporter_id = reporter_id;
    
    IF existing_report.id IS NOT NULL THEN
        RETURN json_build_object('success', false, 'error', 'Already reported');
    END IF;
    
    -- إدراج الإبلاغ
    INSERT INTO comment_reports (comment_id, reporter_id, reported_user_id, reason, description)
    VALUES (p_comment_id, reporter_id, reported_user_id, p_reason, p_description);
    
    result := json_build_object('success', true, 'message', 'Report submitted successfully');
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. دالة لحظر مستخدم
CREATE OR REPLACE FUNCTION block_user(
    p_blocked_id UUID
)
RETURNS JSON AS $$
DECLARE
    blocker_id UUID;
    existing_block comment_blocks%ROWTYPE;
    result JSON;
BEGIN
    -- الحصول على معرف الحاظر
    blocker_id := auth.uid();
    IF blocker_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User not authenticated');
    END IF;
    
    -- منع حظر النفس
    IF blocker_id = p_blocked_id THEN
        RETURN json_build_object('success', false, 'error', 'Cannot block yourself');
    END IF;
    
    -- فحص وجود حظر سابق
    SELECT * INTO existing_block 
    FROM comment_blocks 
    WHERE blocker_id = blocker_id AND blocked_id = p_blocked_id;
    
    IF existing_block.id IS NOT NULL THEN
        RETURN json_build_object('success', false, 'error', 'User already blocked');
    END IF;
    
    -- إدراج الحظر
    INSERT INTO comment_blocks (blocker_id, blocked_id)
    VALUES (blocker_id, p_blocked_id);
    
    result := json_build_object('success', true, 'message', 'User blocked successfully');
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. دالة لإلغاء حظر مستخدم
CREATE OR REPLACE FUNCTION unblock_user(
    p_blocked_id UUID
)
RETURNS JSON AS $$
DECLARE
    blocker_id UUID;
    result JSON;
BEGIN
    blocker_id := auth.uid();
    IF blocker_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User not authenticated');
    END IF;
    
    DELETE FROM comment_blocks 
    WHERE blocker_id = blocker_id AND blocked_id = p_blocked_id;
    
    result := json_build_object('success', true, 'message', 'User unblocked successfully');
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة لحذف تعليق (للمالك فقط)
CREATE OR REPLACE FUNCTION delete_comment(
    p_comment_id UUID
)
RETURNS JSON AS $$
DECLARE
    user_id UUID;
    comment_owner UUID;
    result JSON;
BEGIN
    user_id := auth.uid();
    IF user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User not authenticated');
    END IF;
    
    -- فحص ملكية التعليق
    SELECT comments.user_id INTO comment_owner 
    FROM comments 
    WHERE id = p_comment_id AND is_deleted = FALSE;
    
    IF comment_owner IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'Comment not found');
    END IF;
    
    IF comment_owner != user_id THEN
        RETURN json_build_object('success', false, 'error', 'Not authorized to delete this comment');
    END IF;
    
    -- حذف منطقي
    UPDATE comments 
    SET is_deleted = TRUE, deleted_at = NOW(), deleted_by = user_id
    WHERE id = p_comment_id;
    
    result := json_build_object('success', true, 'message', 'Comment deleted successfully');
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. دالة لتعديل تعليق (للمالك فقط)
CREATE OR REPLACE FUNCTION edit_comment(
    p_comment_id UUID,
    p_new_content TEXT
)
RETURNS JSON AS $$
DECLARE
    user_id UUID;
    comment_owner UUID;
    comment_type TEXT;
    result JSON;
BEGIN
    user_id := auth.uid();
    IF user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User not authenticated');
    END IF;
    
    -- فحص ملكية التعليق ونوعه
    SELECT comments.user_id, comments.type INTO comment_owner, comment_type
    FROM comments 
    WHERE id = p_comment_id AND is_deleted = FALSE;
    
    IF comment_owner IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'Comment not found');
    END IF;
    
    IF comment_owner != user_id THEN
        RETURN json_build_object('success', false, 'error', 'Not authorized to edit this comment');
    END IF;
    
    -- السماح بالتعديل للتعليقات النصية فقط
    IF comment_type != 'text' THEN
        RETURN json_build_object('success', false, 'error', 'Only text comments can be edited');
    END IF;
    
    -- تحديث التعليق
    UPDATE comments 
    SET content = p_new_content, 
        edited_at = NOW(), 
        edit_count = edit_count + 1
    WHERE id = p_comment_id;
    
    result := json_build_object('success', true, 'message', 'Comment updated successfully');
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. RLS (Row Level Security)
ALTER TABLE comment_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_blocks ENABLE ROW LEVEL SECURITY;

-- سياسات comment_reports
CREATE POLICY "Users can view their own reports" ON comment_reports
    FOR SELECT USING (reporter_id = auth.uid());

CREATE POLICY "Users can create reports" ON comment_reports
    FOR INSERT WITH CHECK (reporter_id = auth.uid());

-- سياسات comment_blocks
CREATE POLICY "Users can view their own blocks" ON comment_blocks
    FOR SELECT USING (blocker_id = auth.uid());

CREATE POLICY "Users can create blocks" ON comment_blocks
    FOR INSERT WITH CHECK (blocker_id = auth.uid());

CREATE POLICY "Users can delete their own blocks" ON comment_blocks
    FOR DELETE USING (blocker_id = auth.uid());

-- 11. فيو للتعليقات مع فلترة المحظورين
CREATE OR REPLACE VIEW comments_filtered AS
SELECT c.*
FROM comments c
WHERE c.is_deleted = FALSE
  AND c.user_id NOT IN (
    SELECT blocked_id 
    FROM comment_blocks 
    WHERE blocker_id = auth.uid()
  );

-- تم الانتهاء من إعداد أمان التعليقات
-- الدوال المتاحة:
-- 1. report_comment(comment_id, reason, description)
-- 2. block_user(user_id)
-- 3. unblock_user(user_id)
-- 4. delete_comment(comment_id)
-- 5. edit_comment(comment_id, new_content)
