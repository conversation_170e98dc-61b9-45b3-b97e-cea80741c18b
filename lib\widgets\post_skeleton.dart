import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// ويدجت هيكل وهمي (Skeleton) لتمثيل المنشور أثناء التحميل.
class PostSkeleton extends StatelessWidget {
  const PostSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final baseColor = Colors.grey.shade300;
    final highlightColor = Colors.grey.shade100;
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Shimmer.fromColors(
          baseColor: baseColor,
          highlightColor: highlightColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صف الصورة الرمزية والاسم
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      height: 12,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // صورة/ميديا منشور وهمية
              Container(
                height: 180,
                width: double.infinity,
                color: Colors.white,
              ),
              const SizedBox(height: 12),
              // أسطر نصية قصيرة
              Container(height: 10, width: double.infinity, color: Colors.white),
              const SizedBox(height: 6),
              Container(height: 10, width: MediaQuery.of(context).size.width * 0.6, color: Colors.white),
            ],
          ),
        ),
      ),
    );
  }
} 