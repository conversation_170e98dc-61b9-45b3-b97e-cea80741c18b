import 'message.dart';

class Chat {
  final String id;
  final String otherId;
  final String otherName;
  final String otherAvatar;
  String lastMessage;
  DateTime? lastAt;
  List<Message> messages;
  int unreadCount;

  Chat({
    required this.id,
    required this.otherId,
    required this.otherName,
    required this.otherAvatar,
    required this.lastMessage,
    this.lastAt,
    this.messages = const [],
    this.unreadCount = 0,
  });
} 