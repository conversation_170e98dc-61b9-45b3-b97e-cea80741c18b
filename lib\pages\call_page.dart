import 'package:flutter/material.dart';
import '../supabase_service.dart';

class CallPage extends StatefulWidget {
  final String callId;
  final String otherName;
  final String type; // voice or video
  const CallPage({super.key, required this.callId, required this.otherName, required this.type});

  @override
  State<CallPage> createState() => _CallPageState();
}

class _CallPageState extends State<CallPage> {
  late final stream = SupabaseService().callStatusStream(widget.callId);
  String status = 'ringing';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: StreamBuilder<String>(
        stream: stream,
        builder: (context, snapshot) {
          if (snapshot.hasData) status = snapshot.data!;
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.person, size: 100, color: Colors.white),
                const SizedBox(height: 10),
                Text(widget.otherName, style: const TextStyle(color: Colors.white, fontSize: 24)),
                const SizedBox(height: 10),
                Text(status, style: const TextStyle(color: Colors.white70)),
                const SizedBox(height: 30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (status == 'ringing')
                      FloatingActionButton(
                        backgroundColor: Colors.green,
                        child: const Icon(Icons.call),
                        onPressed: () => SupabaseService().updateCallStatus(widget.callId, 'accepted'),
                      ),
                    const SizedBox(width: 20),
                    FloatingActionButton(
                      backgroundColor: Colors.red,
                      child: const Icon(Icons.call_end),
                      onPressed: () {
                        SupabaseService().updateCallStatus(widget.callId, 'ended');
                        Navigator.pop(context);
                      },
                    ),
                  ],
                )
              ],
            ),
          );
        },
      ),
    );
  }
} 