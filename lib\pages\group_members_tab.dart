import 'package:flutter/material.dart';
import '../supabase_service.dart';

class GroupMembersTab extends StatefulWidget {
  final String groupId;
  final bool isAdmin;
  const GroupMembersTab({super.key, required this.groupId, required this.isAdmin});

  @override
  State<GroupMembersTab> createState() => _GroupMembersTabState();
}

class _GroupMembersTabState extends State<GroupMembersTab> {
  late Future<List<Map<String, dynamic>>> _future;

  @override
  void initState() {
    super.initState();
    _load();
  }

  void _load() {
    setState(() => _future = SupabaseService().fetchGroupMembers(widget.groupId));
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _future,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
        final members = snapshot.data!;
        if (members.isEmpty) return const Center(child: Text('لا يوجد أعضاء'));
        return RefreshIndicator(
          onRefresh: () async => _load(),
          child: ListView.separated(
            itemCount: members.length,
            separatorBuilder: (_, __) => const Divider(height: 1),
            itemBuilder: (ctx, i) {
              final m = members[i];
              return ListTile(
                leading: CircleAvatar(backgroundImage: NetworkImage(m['avatar'] ?? '')),
                title: Text(m['name'] ?? ''),
                subtitle: Text(m['role'] == 'admin' ? 'مشرف' : 'عضو'),
                trailing: widget.isAdmin && !m['isMe']
                    ? PopupMenuButton<String>(
                        onSelected: (v) async {
                          if (v == 'promote') {
                            await SupabaseService().promoteAdmin(widget.groupId, m['id'], m['role'] != 'admin');
                          } else if (v == 'kick') {
                            await SupabaseService().kickMember(widget.groupId, m['id']);
                          }
                          _load();
                        },
                        itemBuilder: (_) => [
                          PopupMenuItem(
                            value: 'promote',
                            child: Text(m['role'] == 'admin' ? 'إلغاء الإشراف' : 'ترقية لمشرف'),
                          ),
                          const PopupMenuItem(value: 'kick', child: Text('طرد')),
                        ],
                      )
                    : null,
              );
            },
          ),
        );
      },
    );
  }
} 