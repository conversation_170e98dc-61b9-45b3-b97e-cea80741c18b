import 'package:flutter/material.dart';
import '../models/reaction_type.dart';

class ReactionDetailsSheet extends StatefulWidget {
  final Map<ReactionType, List<Map<String, dynamic>>> reactionDetails;
  final Map<ReactionType, int> reactionCounts;

  const ReactionDetailsSheet({
    super.key,
    required this.reactionDetails,
    required this.reactionCounts,
  });

  @override
  State<ReactionDetailsSheet> createState() => _ReactionDetailsSheetState();
}

class _ReactionDetailsSheetState extends State<ReactionDetailsSheet>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<ReactionType> _availableReactions = [];

  @override
  void initState() {
    super.initState();
    _availableReactions = widget.reactionCounts.keys.toList()
      ..sort((a, b) => widget.reactionCounts[b]!.compareTo(widget.reactionCounts[a]!));
    // +1 for the "All" tab
    _tabController = TabController(length: _availableReactions.length + 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // حساب إجمالي التفاعلات
    final totalReactions = widget.reactionCounts.values.fold<int>(0, (a, b) => a + b);

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Header with total count
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                const Text(
                  'التفاعلات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '$totalReactions',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Reaction summary bar (like Facebook)
          if (_availableReactions.isNotEmpty) ...[
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // All tab
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _tabController.animateTo(0),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: _tabController.index == 0 ? Colors.blue : Colors.transparent,
                              width: 2,
                            ),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'الكل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: _tabController.index == 0 ? Colors.blue : Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Individual reaction tabs
                  ..._availableReactions.asMap().entries.map((entry) {
                    final index = entry.key + 1; // +1 because "All" is index 0
                    final reaction = entry.value;
                    final count = widget.reactionCounts[reaction] ?? 0;

                    return Expanded(
                      child: GestureDetector(
                        onTap: () => _tabController.animateTo(index),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: _tabController.index == index ? Colors.blue : Colors.transparent,
                                width: 2,
                              ),
                            ),
                          ),
                          child: Center(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Image.asset(
                                  reaction.assetPath,
                                  width: 20,
                                  height: 20,
                                  fit: BoxFit.contain,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$count',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: _tabController.index == index ? Colors.blue : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
            const Divider(height: 1),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // All reactions tab
                  _buildAllUsersList(),
                  // Individual reaction tabs
                  ..._availableReactions.map((reaction) {
                    final users = widget.reactionDetails[reaction] ?? [];
                    return _buildUsersList(users, reaction);
                  }),
                ],
              ),
            ),
          ] else ...[
            const Expanded(
              child: Center(
                child: Text(
                  'لا توجد تفاعلات بعد',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAllUsersList() {
    // جمع جميع المستخدمين من كل التفاعلات
    final allUsers = <Map<String, dynamic>>[];

    for (final entry in widget.reactionDetails.entries) {
      final reaction = entry.key;
      final users = entry.value;

      for (final user in users) {
        allUsers.add({
          ...user,
          'reaction_type': reaction,
        });
      }
    }

    if (allUsers.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد تفاعلات بعد',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: allUsers.length,
      itemBuilder: (context, index) {
        final user = allUsers[index];
        final reaction = user['reaction_type'] as ReactionType;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            leading: CircleAvatar(
              backgroundImage: user['avatar_url'] != null && user['avatar_url'].isNotEmpty
                  ? NetworkImage(user['avatar_url'])
                  : null,
              backgroundColor: Colors.grey[300],
              radius: 22,
              child: user['avatar_url'] == null || user['avatar_url'].isEmpty
                  ? Icon(Icons.person, color: Colors.grey[600])
                  : null,
            ),
            title: Text(
              user['name'] ?? 'مستخدم',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            subtitle: user['username'] != null
                ? Text(
                    '@${user['username']}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  )
                : null,
            trailing: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              child: Image.asset(
                reaction.assetPath,
                width: 24,
                height: 24,
                fit: BoxFit.contain,
              ),
            ),
            onTap: () {
              // يمكن إضافة التنقل إلى صفحة المستخدم هنا
              Navigator.pop(context);
              // Navigator.push(context, MaterialPageRoute(
              //   builder: (_) => ProfilePage(userId: user['id'], username: user['username'] ?? ''),
              // ));
            },
          ),
        );
      },
    );
  }

  Widget _buildUsersList(List<Map<String, dynamic>> users, ReactionType reaction) {
    if (users.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد تفاعلات من هذا النوع',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            leading: CircleAvatar(
              backgroundImage: user['avatar_url'] != null && user['avatar_url'].isNotEmpty
                  ? NetworkImage(user['avatar_url'])
                  : null,
              backgroundColor: Colors.grey[300],
              radius: 22,
              child: user['avatar_url'] == null || user['avatar_url'].isEmpty
                  ? Icon(Icons.person, color: Colors.grey[600])
                  : null,
            ),
            title: Text(
              user['name'] ?? 'مستخدم',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            subtitle: user['username'] != null
                ? Text(
                    '@${user['username']}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  )
                : null,
            trailing: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              child: Image.asset(
                reaction.assetPath,
                width: 24,
                height: 24,
                fit: BoxFit.contain,
              ),
            ),
            onTap: () {
              // يمكن إضافة التنقل إلى صفحة المستخدم هنا
              Navigator.pop(context);
              // Navigator.push(context, MaterialPageRoute(
              //   builder: (_) => ProfilePage(userId: user['id'], username: user['username'] ?? ''),
              // ));
            },
          ),
        );
      },
    );
  }
}
