// Generates simple 96×96 PNG placeholders for each reaction icon.
// Run with:
//   dart run tool/reaction_icon_generator.dart
// Requires the `image` package (already in dev_dependencies).

import 'dart:io';
import 'package:image/image.dart' as img;

void main() {
  // Map reaction name → background color (RGB) & emoji/text label
  const reactions = <String, Map<String, dynamic>>{
    'like':        {'color': 0xFF2196F3, 'label': '👍'},
    'celebrate':   {'color': 0xFF4CAF50, 'label': '🎉'},
    'support':     {'color': 0xFF00BCD4, 'label': '🤝'},
    'love':        {'color': 0xFFE91E63, 'label': '❤'},
    'insightful':  {'color': 0xFFFFC107, 'label': '💡'},
    'funny':       {'color': 0xFFFF9800, 'label': '😆'},
    'wow':         {'color': 0xFFFF9800, 'label': '😮'},
    'curious':     {'color': 0xFF9C27B0, 'label': '?'},
    'sad':         {'color': 0xFF9E9E9E, 'label': '😢'},
    'angry':       {'color': 0xFFD32F2F, 'label': '😠'},
  };

  const size = 96;

  for (final entry in reactions.entries) {
    final name = entry.key;
    final color = entry.value['color'] as int;

    final image = img.Image(width: size, height: size);
    final r = (color >> 16) & 0xFF;
    final g = (color >> 8) & 0xFF;
    final b = color & 0xFF;
    img.fillRect(image,
        x1: 0,
        y1: 0,
        x2: size,
        y2: size,
        color: img.ColorRgb8(r, g, b));

    final bytes = img.encodePng(image);
    final outPath = 'assets/reactions/$name.png';
    File(outPath)
      ..createSync(recursive: true)
      ..writeAsBytesSync(bytes);
    print('Generated $outPath');
  }

  print('\nDone. Add "assets/reactions/" to pubspec.yaml and run flutter pub get.');
} 