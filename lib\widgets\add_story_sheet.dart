import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';

import '../supabase_service.dart';
import '../models/story.dart';

class AddStorySheet extends StatefulWidget {
  const AddStorySheet({super.key});

  @override
  State<AddStorySheet> createState() => _AddStorySheetState();
}

class _AddStorySheetState extends State<AddStorySheet> {
  final TextEditingController _textController = TextEditingController();
  StoryType _type = StoryType.text;
  String? _mediaPath;
  final ImagePicker _picker = ImagePicker();
  bool _sending = false;

  Future<void> _pickMedia(bool isVideo) async {
    final XFile? file = isVideo
        ? await _picker.pickVideo(source: ImageSource.gallery)
        : await _picker.pickImage(source: ImageSource.gallery);
    if (file != null) {
      setState(() {
        _mediaPath = file.path;
        _type = isVideo ? StoryType.video : StoryType.image;
      });
    }
  }

  Future<void> _submit() async {
    if (_sending) return;
    if (_type == StoryType.text && _textController.text.trim().isEmpty) return;
    if ((_type == StoryType.image || _type == StoryType.video) && _mediaPath == null) return;

    setState(() => _sending = true);
    Uint8List? bytes;
    String? ext;
    if (_mediaPath != null) {
      bytes = await File(_mediaPath!).readAsBytes();
      ext = _mediaPath!.split('.').last;
    }
    try {
      await SupabaseService().createStory(
        type: _type,
        text: _type == StoryType.text ? _textController.text.trim() : null,
        mediaBytes: bytes,
        mediaExt: ext,
      );
      if (mounted) Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل نشر القصة: $e')));
      }
    }
    if (mounted) setState(() => _sending = false);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('إنشاء قصة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              if (_type == StoryType.text)
                TextField(
                  controller: _textController,
                  decoration: const InputDecoration(hintText: 'اكتب قصتك...'),
                  maxLines: null,
                ),
              if (_mediaPath != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: _type == StoryType.image
                      ? Image.file(File(_mediaPath!), height: 200, fit: BoxFit.cover)
                      : const Icon(Icons.video_file, size: 80),
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => setState(() => _type = StoryType.text),
                    icon: const Icon(Icons.text_fields),
                    label: const Text('نص'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _pickMedia(false),
                    icon: const Icon(Icons.image),
                    label: const Text('صورة'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _pickMedia(true),
                    icon: const Icon(Icons.videocam),
                    label: const Text('فيديو'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _sending ? null : _submit,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red, minimumSize: const Size.fromHeight(45)),
                child: _sending
                    ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                    : const Text('نشر', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 