import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';

class CommentReactionButton extends StatefulWidget {
  final Comment comment;
  final Function(Comment, ReactionType) onReaction;

  const CommentReactionButton({
    super.key,
    required this.comment,
    required this.onReaction,
  });

  @override
  State<CommentReactionButton> createState() => _CommentReactionButtonState();
}

class _CommentReactionButtonState extends State<CommentReactionButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  OverlayEntry? _overlayEntry;
  bool _isShowingReactions = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _hideReactions();
    super.dispose();
  }

  void _showReactions(BuildContext context) {
    if (_isShowingReactions) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 100,
        top: position.dy - 60,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: ReactionType.values
                  .where((r) => r != ReactionType.none)
                  .map((reaction) => _buildReactionIcon(reaction))
                  .toList(),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isShowingReactions = true;

    // إخفاء التفاعلات بعد 3 ثوان
    Future.delayed(const Duration(seconds: 3), () {
      _hideReactions();
    });
  }

  void _hideReactions() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isShowingReactions = false;
    }
  }

  Widget _buildReactionIcon(ReactionType reaction) {
    return GestureDetector(
      onTap: () {
        _hideReactions();
        widget.onReaction(widget.comment, reaction);
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.all(4),
        child: Image.asset(
          reaction.assetPath,
          width: 24,
          height: 24,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasReaction = widget.comment.currentUserReaction != ReactionType.none;
    final totalReactions = widget.comment.reactionCounts.values
        .fold<int>(0, (sum, count) => sum + count);

    return GestureDetector(
      onTap: () {
        // ضغطة عادية - تفاعل لايك أو إزالة التفاعل
        final reaction = hasReaction ? ReactionType.none : ReactionType.like;
        widget.onReaction(widget.comment, reaction);
        
        if (!hasReaction) {
          _animationController.forward().then((_) {
            _animationController.reverse();
          });
        }
      },
      onLongPress: () {
        // ضغطة طويلة - إظهار جميع التفاعلات
        _showReactions(context);
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة التفاعل
                if (hasReaction)
                  Image.asset(
                    widget.comment.currentUserReaction.assetPath,
                    width: 16,
                    height: 16,
                  )
                else
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                
                // عدد التفاعلات
                if (totalReactions > 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '$totalReactions',
                    style: TextStyle(
                      fontSize: 12,
                      color: hasReaction ? Colors.blue[600] : Colors.grey[600],
                      fontWeight: hasReaction ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}

// Widget لعرض ملخص التفاعلات (مثل المنشورات)
class CommentReactionsSummary extends StatelessWidget {
  final Comment comment;
  final VoidCallback? onTap;

  const CommentReactionsSummary({
    super.key,
    required this.comment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final reactionCounts = comment.reactionCounts;
    final totalReactions = reactionCounts.values.fold<int>(0, (sum, count) => sum + count);
    
    if (totalReactions == 0) return const SizedBox.shrink();

    // ترتيب التفاعلات حسب العدد
    final sortedReactions = reactionCounts.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // عرض أكثر 3 تفاعلات
    final topReactions = sortedReactions.take(3).toList();

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونات التفاعلات المتداخلة
          SizedBox(
            width: topReactions.length == 1 
                ? 16 
                : topReactions.length == 2 
                    ? 28 
                    : 40,
            height: 16,
            child: Stack(
              children: topReactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reactionEntry = entry.value;
                return Positioned(
                  right: index * 12.0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        reactionEntry.key.assetPath,
                        width: 16,
                        height: 16,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          const SizedBox(width: 6),
          
          // العدد الإجمالي
          Text(
            '$totalReactions',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
