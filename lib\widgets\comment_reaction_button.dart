import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';

class CommentReactionButton extends StatefulWidget {
  final Comment comment;
  final Function(Comment, ReactionType) onReaction;

  const CommentReactionButton({
    super.key,
    required this.comment,
    required this.onReaction,
  });

  @override
  State<CommentReactionButton> createState() => _CommentReactionButtonState();
}

class _CommentReactionButtonState extends State<CommentReactionButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  OverlayEntry? _overlayEntry;
  bool _isShowingReactions = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _hideReactions();
    super.dispose();
  }

  void _showReactions(BuildContext context) {
    if (_isShowingReactions) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenWidth = MediaQuery.of(context).size.width;

    // حساب العرض المطلوب للتفاعلات (9 تفاعلات × 32 بكسل + padding)
    const reactionWidth = 32.0;
    const totalReactionsWidth = 9 * reactionWidth + 16; // 9 تفاعلات + padding

    // توسيط التفاعلات أفقياً
    final centerX = position.dx + (size.width / 2);
    final leftPosition = (centerX - (totalReactionsWidth / 2)).clamp(8.0, screenWidth - totalReactionsWidth - 8.0);

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _hideReactions, // إخفاء عند النقر خارج التفاعلات
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.transparent,
          child: Positioned(
            left: leftPosition,
            top: position.dy - 70, // مسافة أكبر من الأعلى
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                      spreadRadius: 2,
                    ),
                  ],
                  border: Border.all(color: Colors.grey[200]!, width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: ReactionType.values
                      .where((r) => r != ReactionType.none)
                      .map((reaction) => _buildReactionIcon(reaction))
                      .toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isShowingReactions = true;

    // إخفاء التفاعلات بعد 5 ثوان (وقت أطول)
    Future.delayed(const Duration(seconds: 5), () {
      if (_isShowingReactions) {
        _hideReactions();
      }
    });
  }

  void _hideReactions() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isShowingReactions = false;
    }
  }

  Widget _buildReactionIcon(ReactionType reaction) {
    return GestureDetector(
      onTap: () {
        _hideReactions();
        widget.onReaction(widget.comment, reaction);
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 1),
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.transparent,
        ),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          child: Image.asset(
            reaction.assetPath,
            width: 28,
            height: 28,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasReaction = widget.comment.currentUserReaction != ReactionType.none;
    final totalReactions = widget.comment.reactionCounts.values
        .fold<int>(0, (sum, count) => sum + count);

    return GestureDetector(
      onTap: () {
        // ضغطة عادية - تفاعل لايك أو إزالة التفاعل الحالي
        if (hasReaction) {
          // إزالة التفاعل الحالي
          widget.onReaction(widget.comment, ReactionType.none);
        } else {
          // إضافة لايك
          widget.onReaction(widget.comment, ReactionType.like);
          _animationController.forward().then((_) {
            _animationController.reverse();
          });
        }
      },
      onLongPress: () {
        // ضغطة طويلة - إظهار جميع التفاعلات
        _showReactions(context);
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة التفاعل - عرض التفاعل الفعلي للمستخدم
                if (hasReaction)
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: _getReactionColor(widget.comment.currentUserReaction).withValues(alpha: 0.1),
                    ),
                    child: Image.asset(
                      widget.comment.currentUserReaction.assetPath,
                      width: 16,
                      height: 16,
                      fit: BoxFit.contain,
                    ),
                  )
                else
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),

                // عدد التفاعلات
                if (totalReactions > 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '$totalReactions',
                    style: TextStyle(
                      fontSize: 12,
                      color: hasReaction
                          ? _getReactionColor(widget.comment.currentUserReaction)
                          : Colors.grey[600],
                      fontWeight: hasReaction ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  // دالة لتحديد لون التفاعل
  Color _getReactionColor(ReactionType reaction) {
    switch (reaction) {
      case ReactionType.like:
        return Colors.blue;
      case ReactionType.love:
        return Colors.red;
      case ReactionType.funny:
        return Colors.orange;
      case ReactionType.angry:
        return Colors.red[800]!;
      case ReactionType.sad:
        return Colors.blue[800]!;
      case ReactionType.celebrate:
        return Colors.purple;
      case ReactionType.support:
        return Colors.green;
      case ReactionType.insightful:
        return Colors.amber[700]!;
      default:
        return Colors.grey;
    }
  }
}

// Widget لعرض ملخص التفاعلات (مثل المنشورات)
class CommentReactionsSummary extends StatelessWidget {
  final Comment comment;
  final VoidCallback? onTap;

  const CommentReactionsSummary({
    super.key,
    required this.comment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final reactionCounts = comment.reactionCounts;
    final totalReactions = reactionCounts.values.fold<int>(0, (sum, count) => sum + count);
    
    if (totalReactions == 0) return const SizedBox.shrink();

    // ترتيب التفاعلات حسب العدد
    final sortedReactions = reactionCounts.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // عرض أكثر 3 تفاعلات
    final topReactions = sortedReactions.take(3).toList();

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونات التفاعلات المتداخلة
          SizedBox(
            width: topReactions.length == 1 
                ? 16 
                : topReactions.length == 2 
                    ? 28 
                    : 40,
            height: 16,
            child: Stack(
              children: topReactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reactionEntry = entry.value;
                return Positioned(
                  right: index * 12.0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        reactionEntry.key.assetPath,
                        width: 16,
                        height: 16,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          const SizedBox(width: 6),
          
          // العدد الإجمالي
          Text(
            '$totalReactions',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
