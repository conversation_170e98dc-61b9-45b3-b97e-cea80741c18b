import 'package:flutter/material.dart';
import 'profile_page.dart';
import 'chat_page.dart';
import '../supabase_service.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  List<Map<String, dynamic>> _users = [];
  final TextEditingController _searchController = TextEditingController();
  final List<String> _recentQueries = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    final data = await SupabaseService().fetchUsers();
    setState(() => _users = data);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدمين...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                filled: true,
                fillColor: Colors.grey[200],
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty && !_recentQueries.contains(value.trim())) {
                  setState(() => _recentQueries.insert(0, value.trim()));
                }
              },
            ),
          ),
          if (_recentQueries.isNotEmpty)
            SizedBox(
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: _recentQueries.take(10).map((q) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ActionChip(
                      label: Text(q),
                      onPressed: () {
                        _searchController.text = q;
                        _searchController.selection = TextSelection.fromPosition(TextPosition(offset: q.length));
                        setState(() {});
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          Expanded(
            child: ListView.builder(
              itemCount: _filtered().length,
              itemBuilder: (context, index) {
                final user = _filtered()[index];
                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ProfilePage(
                                  userId: user['id'],
                                  username: user['name'],
                                ),
                              ),
                            );
                          },
                          child: CircleAvatar(
                            radius: 30,
                            backgroundImage: user['avatar_url'] != ''
                                ? NetworkImage(user['avatar_url'])
                                : const NetworkImage('https://via.placeholder.com/150'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user['name'],
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ElevatedButton(
                              onPressed: () async {
                                final res = await SupabaseService().toggleFollow(user['id']);
                                setState(() {
                                  user['is_following'] = res;
                                  if (user.containsKey('followers_count')) {
                                    user['followers_count'] = (user['followers_count'] as int) + (res ? 1 : -1);
                                  }
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    (user['is_following'] as bool) ? Colors.grey : Colors.red,
                                minimumSize: const Size(100, 36),
                              ),
                              child: Text(
                                (user['is_following'] as bool) ? 'إلغاء المتابعة' : 'متابعة',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.chat),
                              onPressed: () async {
                                final chatId = await SupabaseService().getOrCreateChat(user['id']);
                                if (!mounted) return;
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => ChatPage(
                                      chatId: chatId,
                                      otherId: user['id'],
                                      username: user['name'],
                                      avatarUrl: user['avatar_url'] ?? '',
                                    ),
                                  ),
                                );
                              },
                              color: Colors.red,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _filtered() {
    final query = _searchController.text.trim();
    if (query.isEmpty) return _users;
    return _users.where((u) => (u['name'] as String).contains(query)).toList();
  }
} 