import 'package:flutter/material.dart';
import 'reaction_type.dart';

enum PostType {
  text,
  image,
  video,
  audio,
  voice,
  link,
  shared,
}

class Post {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final DateTime createdAt;
  final PostType type;
  final String? mediaUrl;
  final String? linkUrl;
  final Map<String, dynamic>? linkMeta;
  final Post? originalPost; // للمنشورات المعاد نشرها
  int commentsCount;
  int likesCount;
  int dislikesCount;
  int sharesCount;
  int viewsCount;
  Map<ReactionType, int> reactionCounts;
  final String? bgColor; // hex color string like #FF0000
  ReactionType currentUserReaction;
  bool isSaved;

  Post({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    required this.createdAt,
    required this.type,
    this.mediaUrl,
    this.linkUrl,
    this.linkMeta,
    this.originalPost,
    this.commentsCount = 0,
    this.likesCount = 0,
    this.dislikesCount = 0,
    this.sharesCount = 0,
    this.viewsCount = 0,
    Map<ReactionType, int>? reactionCounts,
    this.bgColor,
    this.currentUserReaction = ReactionType.none,
    this.isSaved = false,
  }) : reactionCounts = reactionCounts ?? {};

  Post copyWith({
    int? likesCount,
    int? dislikesCount,
    int? sharesCount,
    int? commentsCount,
    ReactionType? currentUserReaction,
    String? mediaUrl,
    String? linkUrl,
    Map<String, dynamic>? linkMeta,
    bool? isSaved,
    int? viewsCount,
    Map<ReactionType, int>? reactionCounts,
    String? bgColor,
  }) {
    return Post(
      id: id,
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      content: content,
      createdAt: createdAt,
      type: type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      linkUrl: linkUrl ?? this.linkUrl,
      linkMeta: linkMeta ?? this.linkMeta,
      originalPost: originalPost,
      likesCount: likesCount ?? this.likesCount,
      dislikesCount: dislikesCount ?? this.dislikesCount,
      sharesCount: sharesCount ?? this.sharesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      currentUserReaction: currentUserReaction ?? this.currentUserReaction,
      isSaved: isSaved ?? this.isSaved,
      viewsCount: viewsCount ?? this.viewsCount,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      bgColor: bgColor ?? this.bgColor,
    );
  }
} 