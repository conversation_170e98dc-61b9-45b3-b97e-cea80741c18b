import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/story.dart';
import '../supabase_service.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'profile_page.dart';

class StoriesPage extends StatefulWidget {
  const StoriesPage({super.key});

  @override
  State<StoriesPage> createState() => _StoriesPageState();
}

class _StoriesPageState extends State<StoriesPage> {
  late Stream<List<Story>> _stream;

  @override
  void initState() {
    super.initState();
    _stream = SupabaseService().storiesStream();
  }

  void _openAddStorySheet() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(25))),
      builder: (_) => const _AddStorySheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('القصص', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                IconButton(
                  icon: const Icon(Icons.add_circle, color: Colors.red, size: 28),
                  onPressed: _openAddStorySheet,
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<List<Story>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                final stories = snapshot.data!;
                if (stories.isEmpty) {
                  return const Center(child: Text('لا توجد قصص بعد'));
                }
                return ListView.separated(
                  separatorBuilder: (_, __) => const SizedBox(height: 8),
                  itemCount: stories.length,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemBuilder: (context, index) {
                    final s = stories[index];
                    return _StoryCard(story: s);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _StoryCard extends StatelessWidget {
  final Story story;
  const _StoryCard({required this.story});

  @override
  Widget build(BuildContext context) {
    Widget content;
    switch (story.type) {
      case StoryType.text:
        content = Container(
          width: double.infinity,
          height: 200,
          color: Colors.blue.shade100,
          alignment: Alignment.center,
          child: Text(
            story.text ?? '',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        );
        break;
      case StoryType.image:
        content = GestureDetector(
          onTap: () => _openFull(context, story.mediaUrl ?? ''),
          child: Image.network(
            story.mediaUrl ?? '',
            fit: BoxFit.cover,
            width: double.infinity,
            height: 200,
            loadingBuilder: (context, child, progress) {
              if (progress == null) return child;
              return SizedBox(
                height: 200,
                child: Center(
                  child: CircularProgressIndicator(
                    value: progress.expectedTotalBytes != null
                        ? progress.cumulativeBytesLoaded / progress.expectedTotalBytes!
                        : null,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stack) => const SizedBox(
              height: 200,
              child: Center(child: Icon(Icons.broken_image, size: 60)),
            ),
          ),
        );
        break;
      case StoryType.video:
        content = _VideoStoryPlayer(url: story.mediaUrl ?? '');
        break;
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CircleAvatar(
              backgroundImage: story.userAvatar != '' ? NetworkImage(story.userAvatar) : null,
              child: story.userAvatar == '' ? Text(story.userName[0]) : null,
            ),
            title: Text(story.userName),
            subtitle: Text(_timeAgo(story.createdAt)),
            trailing: _StoryMenu(story: story),
          ),
          content,
        ],
      ),
    );
  }

  String _timeAgo(DateTime dt) {
    final diff = DateTime.now().difference(dt);
    if (diff.inMinutes < 1) return 'الآن';
    if (diff.inMinutes < 60) return 'منذ ${diff.inMinutes} دقيقة';
    if (diff.inHours < 24) return 'منذ ${diff.inHours} ساعة';
    return 'منذ ${diff.inDays} يوم';
  }

  void _openFull(BuildContext context, String url) {
    showDialog(
      context: context,
      builder: (_) => GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: InteractiveViewer(
              child: Image.network(url),
            ),
          ),
        ),
      ),
    );
  }
}

class _VideoStoryPlayer extends StatefulWidget {
  final String url;
  const _VideoStoryPlayer({required this.url});
  @override
  State<_VideoStoryPlayer> createState() => _VideoStoryPlayerState();
}

class _VideoStoryPlayerState extends State<_VideoStoryPlayer> {
  late VideoPlayerController _controller;
  ChewieController? _chewie;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.url)..initialize().then((_) => setState(() {}));
    _chewie = ChewieController(videoPlayerController: _controller, autoPlay: false, looping: false);
  }

  @override
  void dispose() {
    _chewie?.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_controller.value.isInitialized) {
      return const SizedBox(height: 200, child: Center(child: CircularProgressIndicator()));
    }
    return SizedBox(
      height: 200,
      child: Chewie(controller: _chewie!),
    );
  }
}

class _AddStorySheet extends StatefulWidget {
  const _AddStorySheet();

  @override
  State<_AddStorySheet> createState() => _AddStorySheetState();
}

class _AddStorySheetState extends State<_AddStorySheet> {
  final TextEditingController _textController = TextEditingController();
  StoryType _type = StoryType.text;
  String? _mediaPath;
  final ImagePicker _picker = ImagePicker();
  bool _sending = false;

  Future<void> _pickMedia(bool isVideo) async {
    final XFile? file = isVideo
        ? await _picker.pickVideo(source: ImageSource.gallery)
        : await _picker.pickImage(source: ImageSource.gallery);
    if (file != null) {
      setState(() {
        _mediaPath = file.path;
        _type = isVideo ? StoryType.video : StoryType.image;
      });
    }
  }

  Future<void> _submit() async {
    if (_sending) return;
    if (_type == StoryType.text && _textController.text.trim().isEmpty) return;
    if ((_type == StoryType.image || _type == StoryType.video) && _mediaPath == null) return;

    setState(() => _sending = true);
    Uint8List? bytes;
    String? ext;
    if (_mediaPath != null) {
      bytes = await File(_mediaPath!).readAsBytes();
      ext = _mediaPath!.split('.').last;
    }
    try {
      await SupabaseService().createStory(
        type: _type,
        text: _type == StoryType.text ? _textController.text.trim() : null,
        mediaBytes: bytes,
        mediaExt: ext,
      );
      if (mounted) Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل نشر القصة: $e')));
      }
    }
    if (mounted) setState(() => _sending = false);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('إنشاء قصة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              if (_type == StoryType.text)
                TextField(
                  controller: _textController,
                  decoration: const InputDecoration(hintText: 'اكتب قصتك...'),
                  maxLines: null,
                ),
              if (_mediaPath != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: _type == StoryType.image
                      ? Image.file(File(_mediaPath!), height: 200, fit: BoxFit.cover)
                      : const Icon(Icons.video_file, size: 80),
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => setState(() => _type = StoryType.text),
                    icon: const Icon(Icons.text_fields),
                    label: const Text('نص'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _pickMedia(false),
                    icon: const Icon(Icons.image),
                    label: const Text('صورة'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _pickMedia(true),
                    icon: const Icon(Icons.videocam),
                    label: const Text('فيديو'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _sending ? null : _submit,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red, minimumSize: const Size.fromHeight(45)),
                child: _sending
                    ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                    : const Text('نشر', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StoryMenu extends StatelessWidget {
  final Story story;
  const _StoryMenu({required this.story});

  @override
  Widget build(BuildContext context) {
    final uid = Supabase.instance.client.auth.currentUser?.id;
    final isOwner = uid == story.userId;
    return PopupMenuButton<String>(
      onSelected: (v) async {
        if (v == 'delete') {
          await SupabaseService().deleteStory(story.id);
        } else if (v == 'block') {
          await SupabaseService().blockUser(story.userId);
        } else if (v == 'profile') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => ProfilePage(userId: story.userId, username: story.userName)),
          );
        } else if (v == 'report') {
          _showReportDialog(context);
        }
      },
      itemBuilder: (ctx) {
        if (isOwner) {
          return [
            const PopupMenuItem(value: 'delete', child: Text('حذف القصة')),
          ];
        }
        return [
          const PopupMenuItem(value: 'profile', child: Text('عرض الملف الشخصي')),
          const PopupMenuItem(value: 'block', child: Text('حظر المستخدم')),
          const PopupMenuItem(value: 'report', child: Text('الإبلاغ عن القصة')),
        ];
      },
    );
  }

  void _showReportDialog(BuildContext context) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('إبلاغ عن القصة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: 'سبب الإبلاغ'),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
          TextButton(
            onPressed: () async {
              await SupabaseService().reportStory(story.id, controller.text.trim());
              if (Navigator.canPop(context)) Navigator.pop(context);
            },
            child: const Text('إرسال'),
          ),
        ],
      ),
    );
  }
} 