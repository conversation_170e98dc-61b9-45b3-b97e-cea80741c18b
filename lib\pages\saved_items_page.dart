import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/post.dart';
import '../widgets/post_card.dart';

class SavedItemsPage extends StatefulWidget {
  const SavedItemsPage({super.key});

  @override
  State<SavedItemsPage> createState() => _SavedItemsPageState();
}

class _SavedItemsPageState extends State<SavedItemsPage> {
  late Future<List<Post>> _future;

  @override
  void initState() {
    super.initState();
    _future = SupabaseService().fetchSavedPosts();
  }

  Future<void> _refresh() async {
    setState(() {
      _future = SupabaseService().fetchSavedPosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المحفوظات'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'منشورات'),
              Tab(text: 'صور'),
              Tab(text: 'فيديوهات'),
            ],
          ),
        ),
        body: FutureBuilder<List<Post>>(
          future: _future,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(child: Text('خطأ: ${snapshot.error}'));
            }
            final posts = snapshot.data ?? [];
            final textPosts = posts.where((p) => p.type == PostType.text || p.type == PostType.link).toList();
            final imagePosts = posts.where((p) => p.type == PostType.image).toList();
            final videoPosts = posts.where((p) => p.type == PostType.video).toList();
            final lists = [textPosts, imagePosts, videoPosts];
            return TabBarView(
              children: lists.map((list) {
                if (list.isEmpty) {
                  return const Center(child: Text('لا يوجد عناصر محفوظة'));
                }
                return RefreshIndicator(
                  onRefresh: _refresh,
                  child: ListView.builder(
                    padding: const EdgeInsets.only(bottom: 12),
                    itemCount: list.length,
                    itemBuilder: (ctx, i) {
                      return PostCard(post: list[i], onRefresh: _refresh);
                    },
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }
} 