import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_service.dart';
import 'saved_items_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool notifyFollow = true;
  bool notifyChat = true;
  bool notifyApp = true;
  String postVisibility = 'public';
  String commentPermission = 'everyone';
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final s = await SupabaseService().fetchSettings();
    setState(() {
      notifyFollow = s['notify_follow'] ?? true;
      notifyChat = s['notify_chat'] ?? true;
      notifyApp = s['notify_app'] ?? true;
      postVisibility = s['post_visibility'] ?? 'public';
      commentPermission = s['comment_permission'] ?? 'everyone';
    });
  }

  Future<void> _saveToggles() async {
    await SupabaseService().updateSettings({
      'notify_follow': notifyFollow,
      'notify_chat': notifyChat,
      'notify_app': notifyApp,
      'post_visibility': postVisibility,
      'comment_permission': commentPermission,
    });
  }

  Future<void> _changeEmail() async {
    final newEmail = _emailController.text.trim();
    final password = _passwordController.text.trim();
    if (newEmail.isEmpty || password.isEmpty) return;
    try {
      await SupabaseService().changeEmail(newEmail, password);
      if (context.mounted) Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ: $e')));
    }
  }

  Future<void> _changePassword() async {
    final newPass = _passwordController.text.trim();
    if (newPass.isEmpty) return;
    try {
      await SupabaseService().changePassword(newPass);
      if (context.mounted) Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الإعدادات')),
      body: ListView(
        children: [
          const ListTile(title: Text('الخصوصية', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            title: const Text('من يمكنه رؤية منشوراتي الجديدة؟'),
            subtitle: Text(postVisibility == 'public' ? 'الجميع' : 'المتابِعون فقط'),
            trailing: DropdownButton<String>(
              value: postVisibility,
              items: const [
                DropdownMenuItem(value: 'public', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابِعون فقط')),
              ],
              onChanged: (v) => setState(() => postVisibility = v!),
            ),
          ),
          ListTile(
            title: const Text('من يمكنه التعليق على منشوراتي؟'),
            subtitle: Text(commentPermission == 'everyone' ? 'الجميع' : 'المتابِعون فقط'),
            trailing: DropdownButton<String>(
              value: commentPermission,
              items: const [
                DropdownMenuItem(value: 'everyone', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابِعون فقط')),
              ],
              onChanged: (v) => setState(() => commentPermission = v!),
            ),
          ),
          const Divider(),
          const ListTile(title: Text('الإشعارات', style: TextStyle(fontWeight: FontWeight.bold))),
          SwitchListTile(
            title: const Text('إشعارات المتابعين'),
            value: notifyFollow,
            onChanged: (v) => setState(() => notifyFollow = v),
          ),
          SwitchListTile(
            title: const Text('إشعارات الدردشة'),
            value: notifyChat,
            onChanged: (v) => setState(() => notifyChat = v),
          ),
          SwitchListTile(
            title: const Text('إشعارات التطبيق العامة'),
            value: notifyApp,
            onChanged: (v) => setState(() => notifyApp = v),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              onPressed: _saveToggles,
              child: const Text('حفظ الإعدادات'),
            ),
          ),
          const Divider(),
          const ListTile(title: Text('الحساب', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            title: const Text('المحفوظات'),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const SavedItemsPage()));
            },
          ),
          ListTile(
            title: const Text('تغيير البريد الإلكترونى'),
            onTap: () => _showChangeEmailDialog(),
          ),
          ListTile(
            title: const Text('تغيير كلمة المرور'),
            onTap: () => _showChangePasswordDialog(),
          ),
          ListTile(
            title: const Text('تسجيل الخروج'),
            onTap: () async {
              await SupabaseService().signOut();
              if (context.mounted) Navigator.of(context).pushNamedAndRemoveUntil('/', (_) => false);
            },
          ),
          ListTile(
            title: const Text('حذف الحساب'),
            textColor: Colors.red,
            onTap: () async {
              final ok = await showDialog<bool>(
                context: context,
                builder: (_) => AlertDialog(
                  title: const Text('تأكيد الحذف'),
                  content: const Text('سيتم حذف حسابك نهائياً، هل أنت متأكد؟'),
                  actions: [
                    TextButton(onPressed: () => Navigator.pop(context,false), child: const Text('إلغاء')),
                    TextButton(onPressed: () => Navigator.pop(context,true), child: const Text('حذف', style: TextStyle(color: Colors.red))),
                  ],
                ),
              );
              if (ok == true) {
                await SupabaseService().deleteAccount();
              }
            },
          ),
        ],
      ),
    );
  }

  void _showChangeEmailDialog() {
    _emailController.clear();
    _passwordController.clear();
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تغيير البريد الإلكترونى'),
        content: Column(mainAxisSize: MainAxisSize.min, children: [
          TextField(controller: _emailController, decoration: const InputDecoration(labelText: 'البريد الجديد')),
          TextField(controller: _passwordController, obscureText: true, decoration: const InputDecoration(labelText: 'كلمة المرور الحالية')),
        ]),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
          TextButton(onPressed: _changeEmail, child: const Text('حفظ')),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    _passwordController.clear();
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: TextField(controller: _passwordController, obscureText: true, decoration: const InputDecoration(labelText: 'كلمة المرور الجديدة')),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
          TextButton(onPressed: _changePassword, child: const Text('حفظ')),
        ],
      ),
    );
  }
} 