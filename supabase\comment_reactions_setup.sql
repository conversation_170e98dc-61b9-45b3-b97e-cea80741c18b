-- إعد<PERSON> جداول تفاعلات التعليقات والردود
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- 1. جدول تفاعلات التعليقات
CREATE TABLE IF NOT EXISTS comment_reactions (
    id BIGSERIAL PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('like', 'dislike', 'love', 'laugh', 'angry', 'sad', 'celebrate', 'support', 'insightful')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- فهرس فريد لضمان تفاعل واحد لكل مستخدم لكل تعليق
    UNIQUE(comment_id, user_id)
);

-- 2. إضافة أعمدة عدادات التفاعلات لجدول التعليقات
ALTER TABLE comments 
ADD COLUMN IF NOT EXISTS likes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS dislikes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS love_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS laugh_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS angry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS sad_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS celebrate_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS support_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS insightful_count INTEGER DEFAULT 0;

-- 3. فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_comment_reactions_comment_id ON comment_reactions(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_reactions_user_id ON comment_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_comment_reactions_type ON comment_reactions(type);
CREATE INDEX IF NOT EXISTS idx_comment_reactions_created_at ON comment_reactions(created_at);

-- 4. دالة لتحديث عدادات التفاعلات
CREATE OR REPLACE FUNCTION update_comment_reaction_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان إدراج أو تحديث
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- تحديث العدادات للتعليق الجديد/المحدث
        UPDATE comments SET
            likes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'like'),
            dislikes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'dislike'),
            love_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'love'),
            laugh_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'laugh'),
            angry_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'angry'),
            sad_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'sad'),
            celebrate_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'celebrate'),
            support_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'support'),
            insightful_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = NEW.comment_id AND type = 'insightful')
        WHERE id = NEW.comment_id;
    END IF;
    
    -- إذا كان حذف أو تحديث (للقيمة القديمة)
    IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
        -- تحديث العدادات للتعليق القديم
        UPDATE comments SET
            likes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'like'),
            dislikes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'dislike'),
            love_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'love'),
            laugh_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'laugh'),
            angry_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'angry'),
            sad_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'sad'),
            celebrate_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'celebrate'),
            support_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'support'),
            insightful_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = OLD.comment_id AND type = 'insightful')
        WHERE id = OLD.comment_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 5. تريجر لتحديث العدادات تلقائياً
DROP TRIGGER IF EXISTS comment_reactions_count_trigger ON comment_reactions;
CREATE TRIGGER comment_reactions_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON comment_reactions
    FOR EACH ROW EXECUTE FUNCTION update_comment_reaction_counts();

-- 6. دالة toggle_comment_reaction للتفاعل مع التعليقات
CREATE OR REPLACE FUNCTION toggle_comment_reaction(
    p_user UUID,
    p_comment UUID,
    p_type TEXT
)
RETURNS JSON AS $$
DECLARE
    existing_reaction comment_reactions%ROWTYPE;
    result JSON;
BEGIN
    -- البحث عن تفاعل موجود
    SELECT * INTO existing_reaction 
    FROM comment_reactions 
    WHERE comment_id = p_comment AND user_id = p_user;
    
    IF existing_reaction.id IS NOT NULL THEN
        -- يوجد تفاعل
        IF existing_reaction.type = p_type THEN
            -- نفس التفاعل - إزالة
            DELETE FROM comment_reactions WHERE id = existing_reaction.id;
            result := json_build_object('action', 'removed', 'type', p_type);
        ELSE
            -- تفاعل مختلف - تحديث
            UPDATE comment_reactions 
            SET type = p_type, updated_at = NOW()
            WHERE id = existing_reaction.id;
            result := json_build_object('action', 'updated', 'old_type', existing_reaction.type, 'new_type', p_type);
        END IF;
    ELSE
        -- لا يوجد تفاعل - إضافة جديد
        INSERT INTO comment_reactions (comment_id, user_id, type)
        VALUES (p_comment, p_user, p_type);
        result := json_build_object('action', 'added', 'type', p_type);
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 7. دالة لجلب تفاعلات التعليق مع تفاصيل المستخدمين
CREATE OR REPLACE FUNCTION get_comment_reactions(p_comment_id UUID)
RETURNS TABLE (
    reaction_type TEXT,
    reaction_count BIGINT,
    users JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cr.type as reaction_type,
        COUNT(*) as reaction_count,
        json_agg(
            json_build_object(
                'user_id', p.id,
                'name', p.name,
                'avatar_url', p.avatar_url
            )
        ) as users
    FROM comment_reactions cr
    JOIN profiles p ON cr.user_id = p.id
    WHERE cr.comment_id = p_comment_id
    GROUP BY cr.type
    ORDER BY reaction_count DESC;
END;
$$ LANGUAGE plpgsql;

-- 8. RLS (Row Level Security) للأمان
ALTER TABLE comment_reactions ENABLE ROW LEVEL SECURITY;

-- سياسة القراءة: الجميع يمكنهم رؤية التفاعلات
CREATE POLICY "Anyone can view comment reactions" ON comment_reactions
    FOR SELECT USING (true);

-- سياسة الإدراج: المستخدمون المسجلون فقط
CREATE POLICY "Authenticated users can add comment reactions" ON comment_reactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- سياسة التحديث: المستخدم يمكنه تحديث تفاعلاته فقط
CREATE POLICY "Users can update their own comment reactions" ON comment_reactions
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسة الحذف: المستخدم يمكنه حذف تفاعلاته فقط
CREATE POLICY "Users can delete their own comment reactions" ON comment_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- 9. تحديث العدادات للتعليقات الموجودة (تشغيل مرة واحدة)
DO $$
DECLARE
    comment_record RECORD;
BEGIN
    FOR comment_record IN SELECT id FROM comments LOOP
        UPDATE comments SET
            likes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'like'),
            dislikes_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'dislike'),
            love_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'love'),
            laugh_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'laugh'),
            angry_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'angry'),
            sad_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'sad'),
            celebrate_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'celebrate'),
            support_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'support'),
            insightful_count = (SELECT COUNT(*) FROM comment_reactions WHERE comment_id = comment_record.id AND type = 'insightful')
        WHERE id = comment_record.id;
    END LOOP;
END $$;

-- 10. إنشاء فيو لعرض التعليقات مع التفاعلات
CREATE OR REPLACE VIEW comments_with_reactions AS
SELECT 
    c.*,
    COALESCE(
        json_object_agg(
            cr.type, 
            json_build_object(
                'count', reaction_counts.count,
                'user_reacted', CASE WHEN user_reactions.user_id IS NOT NULL THEN true ELSE false END
            )
        ) FILTER (WHERE cr.type IS NOT NULL),
        '{}'::json
    ) as reactions_summary
FROM comments c
LEFT JOIN (
    SELECT 
        comment_id,
        type,
        COUNT(*) as count
    FROM comment_reactions
    GROUP BY comment_id, type
) reaction_counts ON c.id = reaction_counts.comment_id
LEFT JOIN comment_reactions cr ON c.id = cr.comment_id AND cr.type = reaction_counts.type
LEFT JOIN comment_reactions user_reactions ON c.id = user_reactions.comment_id AND user_reactions.user_id = auth.uid()
GROUP BY c.id, c.post_id, c.parent_id, c.user_id, c.content, c.type, c.media_url, c.created_at, c.likes_count, c.dislikes_count, c.love_count, c.laugh_count, c.angry_count, c.sad_count, c.celebrate_count, c.support_count, c.insightful_count;

-- تم الانتهاء من إعداد جداول تفاعلات التعليقات
-- يمكنك الآن استخدام الدوال التالية:
-- 1. toggle_comment_reaction(user_id, comment_id, reaction_type)
-- 2. get_comment_reactions(comment_id)
-- 3. الاستعلام من comments_with_reactions للحصول على التعليقات مع التفاعلات
